'use client';

import { useEffect, useState } from 'react';
import { Brain, TrendingUp, AlertTriangle, Target, RefreshCw } from 'lucide-react';
import { getConfidenceColor, getConfidenceLabel, getMarketRegimeColor, getMarketRegimeLabel } from '@/lib/utils';
import { useMarketStore } from '@/stores/marketStore';
import { analysisApi } from '@/lib/api';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { AIAnalysis } from '@/types';

export function AIInsights() {
  const { selectedCurrency } = useMarketStore();
  const [insights, setInsights] = useState<AIAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAIInsights = async (symbol: string) => {
    setLoading(true);
    setError(null);

    try {
      const data = await analysisApi.getAIInsights(symbol);
      setInsights(data);
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to fetch AI insights');
      console.error('Error fetching AI insights:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (selectedCurrency) {
      fetchAIInsights(selectedCurrency);
    }
  }, [selectedCurrency]);

  const handleRefresh = () => {
    if (selectedCurrency) {
      fetchAIInsights(selectedCurrency);
    }
  };

  if (loading && !insights) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Brain className="h-6 w-6 text-primary-600 dark:text-primary-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            AI Insights
          </h2>
        </div>
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Brain className="h-6 w-6 text-primary-600 dark:text-primary-400" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              AI Insights
            </h2>
          </div>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
            title="Refresh AI insights"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
        <div className="text-center py-4">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600 dark:text-red-400 mb-3">{error}</p>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Retrying...' : 'Try Again'}
          </button>
        </div>
      </div>
    );
  }

  if (!insights) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Brain className="h-6 w-6 text-primary-600 dark:text-primary-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            AI Insights
          </h2>
        </div>
        <p className="text-gray-500 dark:text-gray-400">No AI insights available for this cryptocurrency.</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Brain className="h-6 w-6 text-primary-600 dark:text-primary-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            AI Insights
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {insights.symbol}
          </span>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(insights.confidence_score)}`}>
            {getConfidenceLabel(insights.confidence_score)} ({insights.confidence_score}%)
          </div>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-1 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
            title="Refresh AI insights"
          >
            <RefreshCw className={`h-3 w-3 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Market Regime */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Market Regime
          </span>
          <div className={`px-3 py-1 rounded-full text-white text-sm font-medium ${getMarketRegimeColor(insights.market_regime?.regime || 'unknown')}`}>
            {getMarketRegimeLabel(insights.market_regime?.regime || 'unknown')}
          </div>
        </div>
      </div>

      {/* Overall Signal */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
            AI Signal
          </span>
          <div className={`px-3 py-1 rounded-full text-white text-sm font-medium ${
            insights.overall_signal === 'BUY' ? 'bg-success-500' :
            insights.overall_signal === 'SELL' ? 'bg-danger-500' : 'bg-gray-500'
          }`}>
            {insights.overall_signal}
          </div>
        </div>
      </div>

      {/* Key Levels */}
      {insights.key_levels && Object.keys(insights.key_levels).length > 0 && (
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
            Key Levels
          </h3>
          <div className="grid grid-cols-3 gap-4">
            {insights.key_levels.support && (
              <div className="text-center">
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Support</div>
                <div className="text-sm font-medium text-success-600">
                  ${insights.key_levels.support.toLocaleString()}
                </div>
              </div>
            )}
            {insights.key_levels.resistance && (
              <div className="text-center">
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Resistance</div>
                <div className="text-sm font-medium text-danger-600">
                  ${insights.key_levels.resistance.toLocaleString()}
                </div>
              </div>
            )}
            {insights.key_levels.target && (
              <div className="text-center">
                <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Target</div>
                <div className="text-sm font-medium text-primary-600">
                  ${insights.key_levels.target.toLocaleString()}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Technical Indicators */}
      {insights.technical_indicators && insights.technical_indicators.length > 0 && (
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
            Key Indicators
          </h3>
          <div className="space-y-2">
            {insights.technical_indicators.map((indicator, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {indicator.name}
                </span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {typeof indicator.value === 'number' ? indicator.value.toFixed(2) : indicator.value}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    indicator.signal === 'BUY' ? 'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200' :
                    indicator.signal === 'SELL' ? 'bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200' :
                    'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                  }`}>
                    {indicator.signal}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* AI Explanation */}
      <div>
        <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">
          AI Analysis
        </h3>
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            {insights.explanation || 'AI analysis is being processed...'}
          </p>
        </div>
      </div>
    </div>
  );
}
