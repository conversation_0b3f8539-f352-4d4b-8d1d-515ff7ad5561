"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/RecentAlerts.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/RecentAlerts.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentAlerts: function() { return /* binding */ RecentAlerts; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RecentAlerts auto */ \n\n\nvar getAlertIcon = function(type) {\n    switch(type){\n        case \"pattern_detected\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n        case \"regime_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        case \"signal_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        case \"price_level\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        default:\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    }\n};\nvar getSeverityColor = function(severity) {\n    switch(severity){\n        case \"high\":\n            return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n        case \"medium\":\n            return \"text-warning-600 bg-warning-50 dark:bg-warning-900/20\";\n        case \"low\":\n            return \"text-primary-600 bg-primary-50 dark:bg-primary-900/20\";\n        default:\n            return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n    }\n};\nfunction RecentAlerts() {\n    var _this = this;\n    var unreadCount = mockAlerts.filter(function(alert) {\n        return !alert.isRead;\n    }).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Recent Alerts\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: unreadCount\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                        children: \"View All\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: mockAlerts.map(function(alert) {\n                    var IconComponent = getAlertIcon(alert.type);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border transition-colors cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 \".concat(alert.isRead ? \"border-gray-200 dark:border-gray-600 opacity-75\" : \"border-primary-200 dark:border-primary-700 bg-primary-50/30 dark:bg-primary-900/10\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 rounded-full \".concat(getSeverityColor(alert.severity)),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 17\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                                    children: alert.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 21\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 dark:text-gray-400\",\n                                                            children: alert.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 23\n                                                        }, _this),\n                                                        !alert.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 25\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: alert.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatTimeAgo)(alert.timestamp)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs px-2 py-1 rounded-full font-medium \".concat(alert.severity === \"high\" ? \"bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200\" : alert.severity === \"medium\" ? \"bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200\" : \"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"),\n                                                    children: alert.severity.toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 15\n                        }, _this)\n                    }, alert.id, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            mockAlerts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 dark:text-gray-400\",\n                        children: \"No recent alerts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_c = RecentAlerts;\nvar _c;\n$RefreshReg$(_c, \"RecentAlerts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/RecentAlerts.tsx\n"));

/***/ })

});