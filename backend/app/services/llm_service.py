"""
LLM service for enhanced AI analysis and insights generation
"""

import json
import asyncio
from typing import Dict, List, Optional
import httpx
from app.core.config import settings
from loguru import logger


class LLMService:
    """Service for integrating with Large Language Models"""
    
    def __init__(self):
        self.openai_api_key = getattr(settings, 'OPENAI_API_KEY', None)
        self.anthropic_api_key = getattr(settings, 'ANTHROPIC_API_KEY', None)
        self.client = httpx.AsyncClient(timeout=30.0)
        
    async def generate_market_analysis(self, symbol: str, technical_data: Dict, 
                                     market_regime: Dict, chart_patterns: List[Dict]) -> str:
        """Generate comprehensive market analysis using LLM"""
        try:
            # Prepare context for LLM
            context = self._prepare_analysis_context(symbol, technical_data, market_regime, chart_patterns)
            
            # Try OpenAI first, then fallback to local analysis
            if self.openai_api_key:
                return await self._generate_openai_analysis(context)
            elif self.anthropic_api_key:
                return await self._generate_anthropic_analysis(context)
            else:
                return self._generate_local_analysis(context)
                
        except Exception as e:
            logger.error(f"Error generating LLM analysis: {e}")
            return self._generate_fallback_analysis(symbol, technical_data, market_regime)
    
    def _prepare_analysis_context(self, symbol: str, technical_data: Dict, 
                                market_regime: Dict, chart_patterns: List[Dict]) -> Dict:
        """Prepare structured context for LLM analysis"""
        
        # Extract key technical indicators
        indicators_summary = {}
        if 'rsi' in technical_data:
            indicators_summary['RSI'] = {
                'value': technical_data['rsi'],
                'signal': technical_data.get('rsi_signal', 'NEUTRAL'),
                'interpretation': 'Overbought' if technical_data['rsi'] > 70 else 'Oversold' if technical_data['rsi'] < 30 else 'Neutral'
            }
        
        if 'macd' in technical_data:
            indicators_summary['MACD'] = {
                'value': technical_data['macd'],
                'signal_line': technical_data.get('macd_signal', 0),
                'signal': technical_data.get('macd_signal_type', 'NEUTRAL'),
                'interpretation': 'Bullish momentum' if technical_data['macd'] > technical_data.get('macd_signal', 0) else 'Bearish momentum'
            }
        
        if 'bb_position' in technical_data:
            bb_pos = technical_data['bb_position']
            indicators_summary['Bollinger_Bands'] = {
                'position': bb_pos,
                'signal': technical_data.get('bb_signal', 'NEUTRAL'),
                'interpretation': 'Near upper band' if bb_pos > 0.8 else 'Near lower band' if bb_pos < 0.2 else 'Middle range'
            }
        
        # Market regime summary
        regime_summary = {
            'current_regime': market_regime.get('regime', 'unknown'),
            'confidence': market_regime.get('confidence', 0),
            'description': market_regime.get('description', ''),
            'characteristics': market_regime.get('characteristics', {})
        }
        
        # Chart patterns summary
        patterns_summary = []
        for pattern in chart_patterns:
            patterns_summary.append({
                'name': pattern.get('name', ''),
                'type': pattern.get('type', ''),
                'confidence': pattern.get('confidence', 0),
                'description': pattern.get('description', ''),
                'target_price': pattern.get('target_price'),
                'stop_loss': pattern.get('stop_loss')
            })
        
        return {
            'symbol': symbol,
            'technical_indicators': indicators_summary,
            'market_regime': regime_summary,
            'chart_patterns': patterns_summary,
            'support_resistance': {
                'support': technical_data.get('nearest_support'),
                'resistance': technical_data.get('nearest_resistance')
            }
        }
    
    async def _generate_openai_analysis(self, context: Dict) -> str:
        """Generate analysis using OpenAI GPT"""
        try:
            prompt = self._create_analysis_prompt(context)
            
            response = await self.client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.openai_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gpt-4",
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are an expert cryptocurrency analyst with deep knowledge of technical analysis, market psychology, and trading strategies. Provide clear, actionable insights based on the technical data provided."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 500,
                    "temperature": 0.7
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                logger.error(f"OpenAI API error: {response.status_code} - {response.text}")
                return self._generate_local_analysis(context)
                
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            return self._generate_local_analysis(context)
    
    async def _generate_anthropic_analysis(self, context: Dict) -> str:
        """Generate analysis using Anthropic Claude"""
        try:
            prompt = self._create_analysis_prompt(context)
            
            response = await self.client.post(
                "https://api.anthropic.com/v1/messages",
                headers={
                    "x-api-key": self.anthropic_api_key,
                    "Content-Type": "application/json",
                    "anthropic-version": "2023-06-01"
                },
                json={
                    "model": "claude-3-sonnet-20240229",
                    "max_tokens": 500,
                    "messages": [
                        {
                            "role": "user",
                            "content": f"You are an expert cryptocurrency analyst. {prompt}"
                        }
                    ]
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['content'][0]['text'].strip()
            else:
                logger.error(f"Anthropic API error: {response.status_code} - {response.text}")
                return self._generate_local_analysis(context)
                
        except Exception as e:
            logger.error(f"Error calling Anthropic API: {e}")
            return self._generate_local_analysis(context)
    
    def _create_analysis_prompt(self, context: Dict) -> str:
        """Create a structured prompt for LLM analysis"""
        symbol = context['symbol']
        indicators = context['technical_indicators']
        regime = context['market_regime']
        patterns = context['chart_patterns']
        sr_levels = context['support_resistance']
        
        prompt = f"""
Analyze {symbol} cryptocurrency based on the following technical data:

MARKET REGIME:
- Current regime: {regime['current_regime']} (confidence: {regime['confidence']}%)
- Description: {regime['description']}

TECHNICAL INDICATORS:
"""
        
        for name, data in indicators.items():
            prompt += f"- {name}: {data['value']} ({data['signal']}) - {data['interpretation']}\n"
        
        if patterns:
            prompt += f"\nCHART PATTERNS:\n"
            for pattern in patterns:
                prompt += f"- {pattern['name']} ({pattern['type']}, confidence: {pattern['confidence']}%): {pattern['description']}\n"
        
        if sr_levels['support'] or sr_levels['resistance']:
            prompt += f"\nKEY LEVELS:\n"
            if sr_levels['support']:
                prompt += f"- Support: ${sr_levels['support']:,.2f}\n"
            if sr_levels['resistance']:
                prompt += f"- Resistance: ${sr_levels['resistance']:,.2f}\n"
        
        prompt += f"""
Please provide a comprehensive analysis including:
1. Current market sentiment and momentum
2. Key technical signals and their implications
3. Potential price targets and risk levels
4. Trading recommendations with rationale
5. Risk factors to monitor

Keep the analysis concise but actionable, suitable for both novice and experienced traders.
"""
        
        return prompt
    
    def _generate_local_analysis(self, context: Dict) -> str:
        """Generate analysis using local logic when LLM is not available"""
        symbol = context['symbol']
        indicators = context['technical_indicators']
        regime = context['market_regime']
        patterns = context['chart_patterns']
        
        analysis_parts = []
        
        # Market regime analysis
        regime_name = regime['current_regime']
        confidence = regime['confidence']
        
        if regime_name == 'trending_up':
            analysis_parts.append(f"{symbol} is in a strong uptrend with {confidence}% confidence. This suggests continued bullish momentum.")
        elif regime_name == 'trending_down':
            analysis_parts.append(f"{symbol} is in a downtrend with {confidence}% confidence. Caution is advised for long positions.")
        elif regime_name == 'consolidation':
            analysis_parts.append(f"{symbol} is consolidating with {confidence}% confidence. Expect sideways movement until a breakout occurs.")
        elif regime_name == 'volatile':
            analysis_parts.append(f"{symbol} is experiencing high volatility with {confidence}% confidence. Risk management is crucial.")
        
        # Technical indicators analysis
        if 'RSI' in indicators:
            rsi_data = indicators['RSI']
            if rsi_data['signal'] == 'BUY':
                analysis_parts.append(f"RSI at {rsi_data['value']:.1f} suggests oversold conditions, indicating potential buying opportunity.")
            elif rsi_data['signal'] == 'SELL':
                analysis_parts.append(f"RSI at {rsi_data['value']:.1f} indicates overbought conditions, suggesting potential selling pressure.")
        
        if 'MACD' in indicators:
            macd_data = indicators['MACD']
            if macd_data['signal'] == 'BUY':
                analysis_parts.append("MACD shows bullish momentum with signal line crossover confirmed.")
            elif macd_data['signal'] == 'SELL':
                analysis_parts.append("MACD indicates bearish momentum with potential trend reversal.")
        
        # Chart patterns analysis
        if patterns:
            bullish_patterns = [p for p in patterns if p['type'] == 'bullish']
            bearish_patterns = [p for p in patterns if p['type'] == 'bearish']
            
            if bullish_patterns:
                pattern_names = [p['name'] for p in bullish_patterns]
                analysis_parts.append(f"Bullish patterns detected: {', '.join(pattern_names)}. These support upward price movement.")
            
            if bearish_patterns:
                pattern_names = [p['name'] for p in bearish_patterns]
                analysis_parts.append(f"Bearish patterns identified: {', '.join(pattern_names)}. These suggest potential downward pressure.")
        
        # Combine analysis
        if analysis_parts:
            return " ".join(analysis_parts)
        else:
            return f"{symbol} technical analysis shows mixed signals. Monitor key support and resistance levels for clearer direction."
    
    def _generate_fallback_analysis(self, symbol: str, technical_data: Dict, market_regime: Dict) -> str:
        """Generate basic fallback analysis when all else fails"""
        regime = market_regime.get('regime', 'unknown')
        confidence = market_regime.get('confidence', 0)
        
        return f"{symbol} is currently in a '{regime}' market regime with {confidence}% confidence. Technical indicators suggest monitoring key levels for potential trading opportunities. Risk management is recommended given current market conditions."
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()


# Global LLM service instance
llm_service: Optional[LLMService] = None

def get_llm_service() -> LLMService:
    """Get or create LLM service instance"""
    global llm_service
    if llm_service is None:
        llm_service = LLMService()
    return llm_service
