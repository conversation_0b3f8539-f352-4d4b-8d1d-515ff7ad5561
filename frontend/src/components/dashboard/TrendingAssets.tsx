'use client';

import { useEffect } from 'react';
import { TrendingUp, TrendingDown, Star, Eye } from 'lucide-react';
import { formatCurrency, formatPercentage, getPriceChangeColor, formatMarketCap } from '@/lib/utils';
import { useMarketStore } from '@/stores/marketStore';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export function TrendingAssets() {
  const {
    trendingCurrencies,
    loading,
    fetchTrendingCurrencies,
    trackCurrency,
    untrackCurrency
  } = useMarketStore();

  useEffect(() => {
    fetchTrendingCurrencies(10);
  }, [fetchTrendingCurrencies]);

  const handleWatchToggle = async (symbol: string, isWatched: boolean) => {
    if (isWatched) {
      await untrackCurrency(symbol);
    } else {
      await trackCurrency(symbol);
    }
  };

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'BUY':
        return 'text-success-600 bg-success-50 dark:bg-success-900/20';
      case 'SELL':
        return 'text-danger-600 bg-danger-50 dark:bg-danger-900/20';
      case 'HOLD':
        return 'text-gray-600 bg-gray-50 dark:bg-gray-700';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-700';
    }
  };

  // Mock AI signals for demonstration (in production, this would come from the AI analysis)
  const getAISignal = (priceChange: number) => {
    if (priceChange > 5) return { signal: 'BUY', confidence: 85 };
    if (priceChange > 0) return { signal: 'BUY', confidence: 65 };
    if (priceChange > -5) return { signal: 'HOLD', confidence: 50 };
    return { signal: 'SELL', confidence: 70 };
  };

  if (loading.isLoading && trendingCurrencies.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-2 mb-6">
          <TrendingUp className="h-6 w-6 text-primary-600 dark:text-primary-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Trending Assets
          </h2>
        </div>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <TrendingUp className="h-6 w-6 text-primary-600 dark:text-primary-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Trending Assets
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          <button className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
            View All
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200 dark:border-gray-700">
              <th className="text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">
                Asset
              </th>
              <th className="text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">
                Price
              </th>
              <th className="text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">
                24h Change
              </th>
              <th className="text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">
                Volume
              </th>
              <th className="text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">
                AI Signal
              </th>
              <th className="text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {trendingCurrencies.length > 0 ? trendingCurrencies.map((asset, index) => {
              const aiSignal = getAISignal(asset.price_change_percentage_24h || 0);

              return (
                <tr
                  key={asset.symbol}
                  className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                >
                  <td className="py-4 px-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                        {asset.image ? (
                          <img
                            src={asset.image}
                            alt={asset.name}
                            className="w-6 h-6 rounded-full"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling!.style.display = 'flex';
                            }}
                          />
                        ) : null}
                        <span
                          className="text-sm font-medium text-white"
                          style={{ display: asset.image ? 'none' : 'flex' }}
                        >
                          {asset.symbol?.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">
                          {asset.symbol}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {asset.name}
                        </div>
                      </div>
                    </div>
                  </td>

                  <td className="py-4 px-2 text-right">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {asset.current_price ? formatCurrency(asset.current_price) : '--'}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {asset.market_cap ? formatMarketCap(asset.market_cap) : '--'}
                    </div>
                  </td>

                  <td className="py-4 px-2 text-right">
                    <div className={`flex items-center justify-end space-x-1 ${getPriceChangeColor(asset.price_change_percentage_24h || 0)}`}>
                      {(asset.price_change_percentage_24h || 0) > 0 ? (
                        <TrendingUp className="h-3 w-3" />
                      ) : (
                        <TrendingDown className="h-3 w-3" />
                      )}
                      <span className="font-medium">
                        {(asset.price_change_percentage_24h || 0) > 0 ? '+' : ''}
                        {asset.price_change_percentage_24h ? formatPercentage(asset.price_change_percentage_24h) : '--'}
                      </span>
                    </div>
                  </td>

                  <td className="py-4 px-2 text-right">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {asset.total_volume ? formatMarketCap(asset.total_volume) : '--'}
                    </div>
                  </td>

                  <td className="py-4 px-2 text-center">
                    <div className="flex flex-col items-center space-y-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSignalColor(aiSignal.signal)}`}>
                        {aiSignal.signal}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {aiSignal.confidence}%
                      </span>
                    </div>
                  </td>

                  <td className="py-4 px-2 text-center">
                    <div className="flex items-center justify-center space-x-2">
                      <button
                        onClick={() => handleWatchToggle(asset.symbol, asset.is_tracked || false)}
                        className={`p-1 rounded transition-colors ${
                          asset.is_tracked
                            ? 'text-warning-500 hover:text-warning-600'
                            : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                        }`}
                        title={asset.is_tracked ? 'Remove from watchlist' : 'Add to watchlist'}
                      >
                        <Star className={`h-4 w-4 ${asset.is_tracked ? 'fill-current' : ''}`} />
                      </button>

                      <button
                        className="p-1 rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                        title="View details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              );
            }) : (
              <tr>
                <td colSpan={6} className="py-8 text-center text-gray-500 dark:text-gray-400">
                  No trending assets available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
