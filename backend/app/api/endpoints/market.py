"""
Market data API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import httpx
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.config import settings
from app.services.unified_market_service import UnifiedMarketService
from app.models.cryptocurrency import Cryptocurrency

router = APIRouter()


@router.get("/cryptocurrencies")
async def get_cryptocurrencies(
    limit: int = Query(100, ge=1, le=250),
    offset: int = Query(0, ge=0),
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get list of cryptocurrencies"""
    try:
        market_service = UnifiedMarketService(db)
        cryptocurrencies = await market_service.get_cryptocurrencies(
            limit=limit, 
            offset=offset, 
            search=search
        )
        
        return {
            "success": True,
            "data": cryptocurrencies,
            "total": len(cryptocurrencies),
            "limit": limit,
            "offset": offset
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cryptocurrencies/{symbol}")
async def get_cryptocurrency(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Get specific cryptocurrency data"""
    try:
        market_service = UnifiedMarketService(db)
        crypto = await market_service.get_cryptocurrency_by_symbol(symbol.upper())
        
        if not crypto:
            raise HTTPException(status_code=404, detail="Cryptocurrency not found")
        
        return {
            "success": True,
            "data": crypto
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cryptocurrencies/{symbol}/price-history")
async def get_price_history(
    symbol: str,
    timeframe: str = Query("1d", regex="^(1h|4h|1d|1w|1M)$"),
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Get price history for a cryptocurrency"""
    try:
        market_service = UnifiedMarketService(db)
        price_data = await market_service.get_price_history(
            symbol=symbol.upper(),
            timeframe=timeframe,
            days=days
        )
        
        return {
            "success": True,
            "data": price_data,
            "symbol": symbol.upper(),
            "timeframe": timeframe,
            "days": days
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market-overview")
async def get_market_overview(db: Session = Depends(get_db)):
    """Get overall market overview"""
    try:
        market_service = UnifiedMarketService(db)
        overview = await market_service.get_market_overview()
        
        return {
            "success": True,
            "data": overview
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/trending")
async def get_trending_cryptocurrencies(
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """Get trending cryptocurrencies"""
    try:
        market_service = UnifiedMarketService(db)
        trending = await market_service.get_trending_cryptocurrencies(limit=limit)
        
        return {
            "success": True,
            "data": trending,
            "limit": limit
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cryptocurrencies/{symbol}/track")
async def track_cryptocurrency(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Add cryptocurrency to tracking list"""
    try:
        market_service = UnifiedMarketService(db)
        result = await market_service.track_cryptocurrency(symbol.upper())
        
        return {
            "success": True,
            "message": f"{symbol.upper()} added to tracking list",
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cryptocurrencies/{symbol}/track")
async def untrack_cryptocurrency(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Remove cryptocurrency from tracking list"""
    try:
        market_service = UnifiedMarketService(db)
        result = await market_service.untrack_cryptocurrency(symbol.upper())
        
        return {
            "success": True,
            "message": f"{symbol.upper()} removed from tracking list",
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/refresh")
async def refresh_market_data(db: Session = Depends(get_db)):
    """Manually refresh market data"""
    try:
        market_service = UnifiedMarketService(db)
        result = await market_service.refresh_market_data()
        
        return {
            "success": True,
            "message": "Market data refresh initiated",
            "data": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
