"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/RecentAlerts.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/RecentAlerts.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentAlerts: function() { return /* binding */ RecentAlerts; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ RecentAlerts auto */ \n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar getAlertIcon = function(type) {\n    switch(type){\n        case \"pattern_detected\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        case \"regime_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n        case \"signal_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n        case \"price_level\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n        default:\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n    }\n};\nvar getSeverityColor = function(severity) {\n    switch(severity){\n        case \"high\":\n            return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n        case \"medium\":\n            return \"text-warning-600 bg-warning-50 dark:bg-warning-900/20\";\n        case \"low\":\n            return \"text-primary-600 bg-primary-50 dark:bg-primary-900/20\";\n        default:\n            return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n    }\n};\nfunction RecentAlerts() {\n    var _this = this;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), alerts = _useState[0], setAlerts = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), loading = _useState1[0], setLoading = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), error = _useState2[0], setError = _useState2[1];\n    var fetchAlerts = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_11__._)(function() {\n            var data, err, _err_response_data, _err_response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_12__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        setLoading(true);\n                        setError(null);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        return [\n                            4,\n                            _lib_api__WEBPACK_IMPORTED_MODULE_3__.alertsApi.getAlerts({\n                                limit: 4\n                            })\n                        ];\n                    case 2:\n                        data = _state.sent();\n                        setAlerts(data);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        err = _state.sent();\n                        setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || \"Failed to fetch alerts\");\n                        console.error(\"Error fetching alerts:\", err);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchAlerts() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        fetchAlerts();\n    }, []);\n    var handleRefresh = function() {\n        fetchAlerts();\n    };\n    var unreadCount = alerts.filter(function(alert) {\n        return !alert.is_read;\n    }).length;\n    if (loading && alerts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                            children: \"Recent Alerts\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Recent Alerts\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: unreadCount\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRefresh,\n                                disabled: loading,\n                                className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50\",\n                                title: \"Refresh alerts\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                                children: \"View All\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: mockAlerts.map(function(alert) {\n                    var IconComponent = getAlertIcon(alert.type);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border transition-colors cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 \".concat(alert.isRead ? \"border-gray-200 dark:border-gray-600 opacity-75\" : \"border-primary-200 dark:border-primary-700 bg-primary-50/30 dark:bg-primary-900/10\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 rounded-full \".concat(getSeverityColor(alert.severity)),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 19\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 17\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                                    children: alert.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 21\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 dark:text-gray-400\",\n                                                            children: alert.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 23\n                                                        }, _this),\n                                                        !alert.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 25\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: alert.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTimeAgo)(alert.timestamp)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs px-2 py-1 rounded-full font-medium \".concat(alert.severity === \"high\" ? \"bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200\" : alert.severity === \"medium\" ? \"bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200\" : \"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"),\n                                                    children: alert.severity.toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 15\n                        }, _this)\n                    }, alert.id, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, _this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            mockAlerts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 dark:text-gray-400\",\n                        children: \"No recent alerts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentAlerts, \"/vV0NdMX2EjB1COSECdOFKFU7M4=\");\n_c = RecentAlerts;\nvar _c;\n$RefreshReg$(_c, \"RecentAlerts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/RecentAlerts.tsx\n"));

/***/ })

});