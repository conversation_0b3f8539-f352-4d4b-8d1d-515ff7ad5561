"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/TrendingAssets.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/TrendingAssets.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrendingAssets: function() { return /* binding */ TrendingAssets; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_marketStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/marketStore */ \"(app-pages-browser)/./src/stores/marketStore.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ TrendingAssets auto */ \n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TrendingAssets() {\n    var _this = this;\n    _s();\n    var _useMarketStore = (0,_stores_marketStore__WEBPACK_IMPORTED_MODULE_3__.useMarketStore)(), trendingCurrencies = _useMarketStore.trendingCurrencies, loading = _useMarketStore.loading, fetchTrendingCurrencies = _useMarketStore.fetchTrendingCurrencies, trackCurrency = _useMarketStore.trackCurrency, untrackCurrency = _useMarketStore.untrackCurrency;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        fetchTrendingCurrencies(10);\n    }, [\n        fetchTrendingCurrencies\n    ]);\n    var handleWatchToggle = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_5__._)(function(symbol, isWatched) {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_6__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!isWatched) return [\n                            3,\n                            2\n                        ];\n                        return [\n                            4,\n                            untrackCurrency(symbol)\n                        ];\n                    case 1:\n                        _state.sent();\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        return [\n                            4,\n                            trackCurrency(symbol)\n                        ];\n                    case 3:\n                        _state.sent();\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function handleWatchToggle(symbol, isWatched) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var getSignalColor = function(signal) {\n        switch(signal){\n            case \"BUY\":\n                return \"text-success-600 bg-success-50 dark:bg-success-900/20\";\n            case \"SELL\":\n                return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n            case \"HOLD\":\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n            default:\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n        }\n    };\n    // Mock AI signals for demonstration (in production, this would come from the AI analysis)\n    var getAISignal = function(priceChange) {\n        if (priceChange > 5) return {\n            signal: \"BUY\",\n            confidence: 85\n        };\n        if (priceChange > 0) return {\n            signal: \"BUY\",\n            confidence: 65\n        };\n        if (priceChange > -5) return {\n            signal: \"HOLD\",\n            confidence: 50\n        };\n        return {\n            signal: \"SELL\",\n            confidence: 70\n        };\n    };\n    if (loading.isLoading && trendingCurrencies.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                            children: \"Trending Assets\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Trending Assets\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                            children: \"View All\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-b border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"24h Change\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"AI Signal\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: mockTrendingAssets.map(function(asset, index) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: asset.symbol.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                children: asset.symbol\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: asset.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(asset.price)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(asset.marketCap)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-1 \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)(asset.change24h)),\n                                                children: [\n                                                    asset.change24h > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 23\n                                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            asset.change24h > 0 ? \"+\" : \"\",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(asset.change24h)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 dark:text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(asset.volume)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getSignalColor(asset.aiSignal)),\n                                                        children: asset.aiSignal\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            asset.confidence,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: function() {\n                                                            return handleWatchToggle(asset.symbol);\n                                                        },\n                                                        className: \"p-1 rounded transition-colors \".concat(asset.isWatched ? \"text-warning-500 hover:text-warning-600\" : \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"),\n                                                        title: asset.isWatched ? \"Remove from watchlist\" : \"Add to watchlist\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(asset.isWatched ? \"fill-current\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-1 rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\",\n                                                        title: \"View details\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, _this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(TrendingAssets, \"mpW+07xTi3L6/aluG9sVUFaJrB4=\", false, function() {\n    return [\n        _stores_marketStore__WEBPACK_IMPORTED_MODULE_3__.useMarketStore\n    ];\n});\n_c = TrendingAssets;\nvar _c;\n$RefreshReg$(_c, \"TrendingAssets\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TrendingAssets.tsx\n"));

/***/ })

});