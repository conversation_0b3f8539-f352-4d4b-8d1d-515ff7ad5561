"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/TrendingAssets.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/TrendingAssets.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrendingAssets: function() { return /* binding */ TrendingAssets; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ TrendingAssets auto */ \n\n\nfunction TrendingAssets() {\n    var _this = this;\n    var handleWatchToggle = function(symbol) {\n        // This will be implemented with state management\n        console.log(\"Toggle watch for \".concat(symbol));\n    };\n    var getSignalColor = function(signal) {\n        switch(signal){\n            case \"BUY\":\n                return \"text-success-600 bg-success-50 dark:bg-success-900/20\";\n            case \"SELL\":\n                return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n            case \"HOLD\":\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n            default:\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Trending Assets\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                            children: \"View All\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-b border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"24h Change\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"AI Signal\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: mockTrendingAssets.map(function(asset, index) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: asset.symbol.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                children: asset.symbol\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 83,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: asset.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency)(asset.price)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatMarketCap)(asset.marketCap)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-1 \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getPriceChangeColor)(asset.change24h)),\n                                                children: [\n                                                    asset.change24h > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 23\n                                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            asset.change24h > 0 ? \"+\" : \"\",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatPercentage)(asset.change24h)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 dark:text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatMarketCap)(asset.volume)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getSignalColor(asset.aiSignal)),\n                                                        children: asset.aiSignal\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            asset.confidence,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: function() {\n                                                            return handleWatchToggle(asset.symbol);\n                                                        },\n                                                        className: \"p-1 rounded transition-colors \".concat(asset.isWatched ? \"text-warning-500 hover:text-warning-600\" : \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"),\n                                                        title: asset.isWatched ? \"Remove from watchlist\" : \"Add to watchlist\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(asset.isWatched ? \"fill-current\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-1 rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\",\n                                                        title: \"View details\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, _this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, _this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_c = TrendingAssets;\nvar _c;\n$RefreshReg$(_c, \"TrendingAssets\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TrendingAssets.tsx\n"));

/***/ })

});