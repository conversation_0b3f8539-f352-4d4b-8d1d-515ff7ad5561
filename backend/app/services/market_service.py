"""
Market data service
"""

import httpx
import asyncio
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from app.core.config import settings
from app.models.cryptocurrency import Cryptocurrency
from loguru import logger


class MarketService:
    """Service for handling market data operations"""

    def __init__(self, db: Session):
        self.db = db
        self.coingecko_url = settings.COINGECKO_API_URL
        self.headers = {
            "User-Agent": "Adaptive-AI-Sentinel/1.0",
            "Accept": "application/json"
        }
        if settings.COINGECKO_API_KEY:
            self.headers["x-cg-demo-api-key"] = settings.COINGECKO_API_KEY
    
    async def get_cryptocurrencies(
        self,
        limit: int = 100,
        offset: int = 0,
        search: Optional[str] = None
    ) -> List[Dict]:
        """Get list of cryptocurrencies from CoinGecko API"""
        try:
            # Fetch from CoinGecko API
            async with httpx.AsyncClient(timeout=30.0) as client:
                params = {
                    "vs_currency": "usd",
                    "order": "market_cap_desc",
                    "per_page": min(limit, 250),  # CoinGecko max is 250
                    "page": (offset // limit) + 1,
                    "sparkline": False,
                    "price_change_percentage": "24h"
                }

                url = f"{self.coingecko_url}/coins/markets"
                logger.info(f"Fetching cryptocurrencies from: {url}")

                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()

                data = response.json()

                # Apply search filter if provided
                if search:
                    search_lower = search.lower()
                    data = [
                        crypto for crypto in data
                        if search_lower in crypto["name"].lower() or search_lower in crypto["symbol"].lower()
                    ]

                # Transform data to match our format
                cryptocurrencies = []
                for crypto in data:
                    cryptocurrencies.append({
                        "id": crypto.get("id"),
                        "symbol": crypto.get("symbol", "").upper(),
                        "name": crypto.get("name"),
                        "current_price": crypto.get("current_price"),
                        "market_cap": crypto.get("market_cap"),
                        "market_cap_rank": crypto.get("market_cap_rank"),
                        "fully_diluted_valuation": crypto.get("fully_diluted_valuation"),
                        "total_volume": crypto.get("total_volume"),
                        "high_24h": crypto.get("high_24h"),
                        "low_24h": crypto.get("low_24h"),
                        "price_change_24h": crypto.get("price_change_24h"),
                        "price_change_percentage_24h": crypto.get("price_change_percentage_24h"),
                        "market_cap_change_24h": crypto.get("market_cap_change_24h"),
                        "market_cap_change_percentage_24h": crypto.get("market_cap_change_percentage_24h"),
                        "circulating_supply": crypto.get("circulating_supply"),
                        "total_supply": crypto.get("total_supply"),
                        "max_supply": crypto.get("max_supply"),
                        "ath": crypto.get("ath"),
                        "ath_change_percentage": crypto.get("ath_change_percentage"),
                        "ath_date": crypto.get("ath_date"),
                        "atl": crypto.get("atl"),
                        "atl_change_percentage": crypto.get("atl_change_percentage"),
                        "atl_date": crypto.get("atl_date"),
                        "image": crypto.get("image"),
                        "last_updated": crypto.get("last_updated")
                    })

                logger.info(f"Successfully fetched {len(cryptocurrencies)} cryptocurrencies")
                return cryptocurrencies

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching cryptocurrencies: {e.response.status_code} - {e.response.text}")
            raise Exception(f"API error: {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"Request error fetching cryptocurrencies: {str(e)}")
            raise Exception(f"Network error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error fetching cryptocurrencies: {str(e)}")
            raise Exception(f"Error fetching cryptocurrencies: {str(e)}")
    
    async def get_cryptocurrency_by_symbol(self, symbol: str) -> Optional[Dict]:
        """Get specific cryptocurrency by symbol from CoinGecko API"""
        try:
            # First try to get from the markets endpoint
            cryptocurrencies = await self.get_cryptocurrencies(limit=250)
            for crypto in cryptocurrencies:
                if crypto["symbol"].upper() == symbol.upper():
                    return crypto

            # If not found, try the coins/list endpoint to get the ID
            async with httpx.AsyncClient(timeout=30.0) as client:
                list_url = f"{self.coingecko_url}/coins/list"
                response = await client.get(list_url, headers=self.headers)
                response.raise_for_status()

                coins_list = response.json()
                coin_id = None

                for coin in coins_list:
                    if coin.get("symbol", "").upper() == symbol.upper():
                        coin_id = coin.get("id")
                        break

                if not coin_id:
                    return None

                # Get detailed data for the specific coin
                detail_url = f"{self.coingecko_url}/coins/{coin_id}"
                params = {
                    "localization": False,
                    "tickers": False,
                    "market_data": True,
                    "community_data": False,
                    "developer_data": False,
                    "sparkline": False
                }

                response = await client.get(detail_url, params=params, headers=self.headers)
                response.raise_for_status()

                coin_data = response.json()
                market_data = coin_data.get("market_data", {})

                return {
                    "id": coin_data.get("id"),
                    "symbol": coin_data.get("symbol", "").upper(),
                    "name": coin_data.get("name"),
                    "current_price": market_data.get("current_price", {}).get("usd"),
                    "market_cap": market_data.get("market_cap", {}).get("usd"),
                    "market_cap_rank": market_data.get("market_cap_rank"),
                    "total_volume": market_data.get("total_volume", {}).get("usd"),
                    "high_24h": market_data.get("high_24h", {}).get("usd"),
                    "low_24h": market_data.get("low_24h", {}).get("usd"),
                    "price_change_24h": market_data.get("price_change_24h"),
                    "price_change_percentage_24h": market_data.get("price_change_percentage_24h"),
                    "circulating_supply": market_data.get("circulating_supply"),
                    "total_supply": market_data.get("total_supply"),
                    "max_supply": market_data.get("max_supply"),
                    "ath": market_data.get("ath", {}).get("usd"),
                    "ath_change_percentage": market_data.get("ath_change_percentage", {}).get("usd"),
                    "ath_date": market_data.get("ath_date", {}).get("usd"),
                    "atl": market_data.get("atl", {}).get("usd"),
                    "atl_change_percentage": market_data.get("atl_change_percentage", {}).get("usd"),
                    "atl_date": market_data.get("atl_date", {}).get("usd"),
                    "image": coin_data.get("image", {}).get("large"),
                    "last_updated": market_data.get("last_updated")
                }

        except Exception as e:
            logger.error(f"Error fetching cryptocurrency {symbol}: {str(e)}")
            raise Exception(f"Error fetching cryptocurrency {symbol}: {str(e)}")
    
    async def get_price_history(
        self,
        symbol: str,
        timeframe: str = "1d",
        days: int = 30
    ) -> List[Dict]:
        """Get price history for a cryptocurrency from CoinGecko API"""
        try:
            # First get the coin ID for the symbol
            crypto = await self.get_cryptocurrency_by_symbol(symbol)
            if not crypto:
                raise Exception(f"Cryptocurrency {symbol} not found")

            coin_id = crypto["id"]

            async with httpx.AsyncClient(timeout=30.0) as client:
                # CoinGecko historical data endpoint
                url = f"{self.coingecko_url}/coins/{coin_id}/market_chart"

                params = {
                    "vs_currency": "usd",
                    "days": days,
                    "interval": "daily" if timeframe in ["1d", "1w"] else "hourly"
                }

                logger.info(f"Fetching price history for {symbol} ({coin_id}) - {days} days")

                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()

                data = response.json()

                # Extract price, market cap, and volume data
                prices = data.get("prices", [])
                market_caps = data.get("market_caps", [])
                volumes = data.get("total_volumes", [])

                price_data = []
                for i, price_point in enumerate(prices):
                    timestamp = int(price_point[0] / 1000)  # Convert from milliseconds
                    price = price_point[1]

                    # Get corresponding market cap and volume
                    market_cap = market_caps[i][1] if i < len(market_caps) else None
                    volume = volumes[i][1] if i < len(volumes) else None

                    # For OHLC data, we'll use the price as close and estimate others
                    # Note: CoinGecko free tier doesn't provide OHLC, only closing prices
                    price_data.append({
                        "timestamp": timestamp,
                        "open": price,  # Using close as open (limitation of free API)
                        "high": price * 1.01,  # Estimated high
                        "low": price * 0.99,   # Estimated low
                        "close": price,
                        "volume": volume,
                        "market_cap": market_cap
                    })

                logger.info(f"Successfully fetched {len(price_data)} price points for {symbol}")
                return price_data

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching price history for {symbol}: {e.response.status_code}")
            raise Exception(f"API error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error fetching price history for {symbol}: {str(e)}")
            raise Exception(f"Error fetching price history for {symbol}: {str(e)}")
    
    async def get_market_overview(self) -> Dict:
        """Get overall market overview from CoinGecko API"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Get global market data
                global_url = f"{self.coingecko_url}/global"

                logger.info("Fetching global market overview")

                response = await client.get(global_url, headers=self.headers)
                response.raise_for_status()

                data = response.json()
                global_data = data.get("data", {})

                # Extract market overview data
                total_market_cap = global_data.get("total_market_cap", {}).get("usd", 0)
                total_volume = global_data.get("total_volume", {}).get("usd", 0)
                market_cap_change_24h = global_data.get("market_cap_change_percentage_24h_usd", 0)

                # Get BTC dominance
                btc_dominance = global_data.get("market_cap_percentage", {}).get("btc", 0)

                # Get active cryptocurrencies and markets
                active_cryptocurrencies = global_data.get("active_cryptocurrencies", 0)
                markets = global_data.get("markets", 0)

                # Calculate volume change (not directly available, so we'll estimate)
                volume_change_24h = market_cap_change_24h * 0.8  # Rough estimation

                # Fear & Greed Index (not available in CoinGecko free tier)
                # We'll calculate a simple sentiment based on market cap change
                if market_cap_change_24h > 5:
                    fear_greed_index = 75  # Extreme Greed
                elif market_cap_change_24h > 2:
                    fear_greed_index = 65  # Greed
                elif market_cap_change_24h > -2:
                    fear_greed_index = 50  # Neutral
                elif market_cap_change_24h > -5:
                    fear_greed_index = 35  # Fear
                else:
                    fear_greed_index = 25  # Extreme Fear

                overview = {
                    "total_market_cap": total_market_cap,
                    "total_volume": total_volume,
                    "btc_dominance": btc_dominance,
                    "market_cap_change_24h": market_cap_change_24h,
                    "volume_change_24h": volume_change_24h,
                    "fear_greed_index": fear_greed_index,
                    "active_cryptocurrencies": active_cryptocurrencies,
                    "markets": markets,
                    "updated_at": global_data.get("updated_at")
                }

                logger.info("Successfully fetched global market overview")
                return overview

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching market overview: {e.response.status_code}")
            raise Exception(f"API error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error fetching market overview: {str(e)}")
            raise Exception(f"Error fetching market overview: {str(e)}")
    
    async def get_trending_cryptocurrencies(self, limit: int = 10) -> List[Dict]:
        """Get trending cryptocurrencies from CoinGecko API"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Get trending coins from CoinGecko
                trending_url = f"{self.coingecko_url}/search/trending"

                logger.info("Fetching trending cryptocurrencies")

                response = await client.get(trending_url, headers=self.headers)
                response.raise_for_status()

                data = response.json()
                trending_coins = data.get("coins", [])

                # Get detailed data for trending coins
                trending_data = []
                for coin_info in trending_coins[:limit]:
                    coin = coin_info.get("item", {})
                    coin_id = coin.get("id")

                    if coin_id:
                        # Get market data for this coin
                        market_url = f"{self.coingecko_url}/coins/{coin_id}"
                        params = {
                            "localization": False,
                            "tickers": False,
                            "market_data": True,
                            "community_data": False,
                            "developer_data": False,
                            "sparkline": False
                        }

                        try:
                            market_response = await client.get(market_url, params=params, headers=self.headers)
                            market_response.raise_for_status()

                            coin_data = market_response.json()
                            market_data = coin_data.get("market_data", {})

                            trending_data.append({
                                "id": coin_data.get("id"),
                                "symbol": coin_data.get("symbol", "").upper(),
                                "name": coin_data.get("name"),
                                "current_price": market_data.get("current_price", {}).get("usd"),
                                "market_cap": market_data.get("market_cap", {}).get("usd"),
                                "market_cap_rank": market_data.get("market_cap_rank"),
                                "price_change_percentage_24h": market_data.get("price_change_percentage_24h"),
                                "total_volume": market_data.get("total_volume", {}).get("usd"),
                                "image": coin_data.get("image", {}).get("large"),
                                "trending_rank": coin.get("market_cap_rank", 999)
                            })

                            # Add small delay to avoid rate limiting
                            await asyncio.sleep(0.1)

                        except Exception as e:
                            logger.warning(f"Failed to get market data for trending coin {coin_id}: {str(e)}")
                            continue

                # If we don't have enough trending data, supplement with top market cap coins
                if len(trending_data) < limit:
                    top_coins = await self.get_cryptocurrencies(limit=limit - len(trending_data))
                    for coin in top_coins:
                        if not any(t["id"] == coin["id"] for t in trending_data):
                            trending_data.append(coin)

                logger.info(f"Successfully fetched {len(trending_data)} trending cryptocurrencies")
                return trending_data[:limit]

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching trending cryptocurrencies: {e.response.status_code}")
            # Fallback to top market cap coins
            return await self.get_cryptocurrencies(limit=limit)
        except Exception as e:
            logger.error(f"Error fetching trending cryptocurrencies: {str(e)}")
            # Fallback to top market cap coins
            return await self.get_cryptocurrencies(limit=limit)
    
    async def track_cryptocurrency(self, symbol: str) -> Dict:
        """Add cryptocurrency to tracking list"""
        try:
            # This would update the database to mark as tracked
            return {"symbol": symbol, "tracked": True}
        except Exception as e:
            raise Exception(f"Error tracking cryptocurrency {symbol}: {str(e)}")
    
    async def untrack_cryptocurrency(self, symbol: str) -> Dict:
        """Remove cryptocurrency from tracking list"""
        try:
            # This would update the database to mark as not tracked
            return {"symbol": symbol, "tracked": False}
        except Exception as e:
            raise Exception(f"Error untracking cryptocurrency {symbol}: {str(e)}")
    
    async def refresh_market_data(self) -> Dict:
        """Manually refresh market data"""
        try:
            # This would trigger a background task to refresh all market data
            return {"status": "refresh_initiated", "message": "Market data refresh started"}
        except Exception as e:
            raise Exception(f"Error refreshing market data: {str(e)}")
