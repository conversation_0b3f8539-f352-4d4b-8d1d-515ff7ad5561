/**
 * Market data store using Zustand
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { CryptoCurrency, AIAnalysis, LoadingState, ChartData } from '@/types';
import { marketApi, analysisApi } from '@/lib/api';
import toast from 'react-hot-toast';

interface MarketStore {
  // State
  currencies: CryptoCurrency[];
  selectedCurrency: string;
  marketOverview: any;
  priceData: Record<string, ChartData[]>;
  aiAnalysis: Record<string, AIAnalysis>;
  trendingCurrencies: CryptoCurrency[];
  loading: LoadingState;
  
  // Actions
  fetchCurrencies: (params?: { limit?: number; offset?: number; search?: string }) => Promise<void>;
  fetchMarketOverview: () => Promise<void>;
  fetchPriceData: (symbol: string, timeframe?: string, days?: number) => Promise<void>;
  fetchAIAnalysis: (symbol: string) => Promise<void>;
  fetchTrendingCurrencies: (limit?: number) => Promise<void>;
  setSelectedCurrency: (symbol: string) => void;
  trackCurrency: (symbol: string) => Promise<void>;
  untrackCurrency: (symbol: string) => Promise<void>;
  refreshAllData: () => Promise<void>;
  clearError: () => void;
}

export const useMarketStore = create<MarketStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      currencies: [],
      selectedCurrency: 'BTC',
      marketOverview: null,
      priceData: {},
      aiAnalysis: {},
      trendingCurrencies: [],
      loading: {
        isLoading: false,
        error: undefined,
      },

      // Actions
      fetchCurrencies: async (params) => {
        set((state) => ({
          loading: { ...state.loading, isLoading: true, error: undefined }
        }));

        try {
          const currencies = await marketApi.getCryptocurrencies(params);
          set({ currencies });
          
          // If no currency is selected, select the first one
          if (!get().selectedCurrency && currencies.length > 0) {
            set({ selectedCurrency: currencies[0].symbol });
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || 'Failed to fetch cryptocurrencies';
          set((state) => ({
            loading: { ...state.loading, error: errorMessage }
          }));
          toast.error(errorMessage);
        } finally {
          set((state) => ({
            loading: { ...state.loading, isLoading: false }
          }));
        }
      },

      fetchMarketOverview: async () => {
        try {
          const overview = await marketApi.getMarketOverview();
          set({ marketOverview: overview });
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || 'Failed to fetch market overview';
          console.error('Market overview error:', errorMessage);
          toast.error(errorMessage);
        }
      },

      fetchPriceData: async (symbol, timeframe = '1d', days = 30) => {
        try {
          const data = await marketApi.getPriceHistory(symbol, timeframe, days);
          set((state) => ({
            priceData: {
              ...state.priceData,
              [symbol]: data
            }
          }));
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || `Failed to fetch price data for ${symbol}`;
          console.error('Price data error:', errorMessage);
          toast.error(errorMessage);
        }
      },

      fetchAIAnalysis: async (symbol) => {
        try {
          const analysis = await analysisApi.getAIInsights(symbol);
          set((state) => ({
            aiAnalysis: {
              ...state.aiAnalysis,
              [symbol]: analysis
            }
          }));
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || `Failed to fetch AI analysis for ${symbol}`;
          console.error('AI analysis error:', errorMessage);
          // Don't show toast for AI analysis errors as they might be expected for some currencies
        }
      },

      fetchTrendingCurrencies: async (limit = 10) => {
        try {
          const trending = await marketApi.getTrending(limit);
          set({ trendingCurrencies: trending });
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || 'Failed to fetch trending cryptocurrencies';
          console.error('Trending currencies error:', errorMessage);
          toast.error(errorMessage);
        }
      },

      setSelectedCurrency: (symbol) => {
        set({ selectedCurrency: symbol });
        
        // Automatically fetch AI analysis for the selected currency
        get().fetchAIAnalysis(symbol);
        get().fetchPriceData(symbol);
      },

      trackCurrency: async (symbol) => {
        try {
          await marketApi.trackCryptocurrency(symbol);
          toast.success(`${symbol} added to watchlist`);
          
          // Update the currency in the list
          set((state) => ({
            currencies: state.currencies.map(currency =>
              currency.symbol === symbol
                ? { ...currency, is_tracked: true }
                : currency
            )
          }));
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || `Failed to track ${symbol}`;
          toast.error(errorMessage);
        }
      },

      untrackCurrency: async (symbol) => {
        try {
          await marketApi.untrackCryptocurrency(symbol);
          toast.success(`${symbol} removed from watchlist`);
          
          // Update the currency in the list
          set((state) => ({
            currencies: state.currencies.map(currency =>
              currency.symbol === symbol
                ? { ...currency, is_tracked: false }
                : currency
            )
          }));
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || `Failed to untrack ${symbol}`;
          toast.error(errorMessage);
        }
      },

      refreshAllData: async () => {
        set((state) => ({
          loading: { ...state.loading, isLoading: true, error: undefined }
        }));

        try {
          // Refresh market data on backend
          await marketApi.refreshMarketData();
          
          // Fetch all data
          await Promise.all([
            get().fetchCurrencies(),
            get().fetchMarketOverview(),
            get().fetchTrendingCurrencies(),
          ]);
          
          // Fetch data for selected currency
          const selectedCurrency = get().selectedCurrency;
          if (selectedCurrency) {
            await Promise.all([
              get().fetchPriceData(selectedCurrency),
              get().fetchAIAnalysis(selectedCurrency),
            ]);
          }
          
          toast.success('Market data refreshed successfully');
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || 'Failed to refresh market data';
          set((state) => ({
            loading: { ...state.loading, error: errorMessage }
          }));
          toast.error(errorMessage);
        } finally {
          set((state) => ({
            loading: { ...state.loading, isLoading: false }
          }));
        }
      },

      clearError: () => {
        set((state) => ({
          loading: { ...state.loading, error: undefined }
        }));
      },
    }),
    {
      name: 'market-store',
    }
  )
);
