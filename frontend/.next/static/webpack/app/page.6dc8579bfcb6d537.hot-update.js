"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/RecentAlerts.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/RecentAlerts.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentAlerts: function() { return /* binding */ RecentAlerts; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,RefreshCw,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ RecentAlerts auto */ \n\n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar getAlertIcon = function(type) {\n    switch(type){\n        case \"pattern_detected\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        case \"regime_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n        case \"signal_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n        case \"price_level\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n        default:\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n    }\n};\nvar getSeverityColor = function(severity) {\n    switch(severity){\n        case \"high\":\n            return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n        case \"medium\":\n            return \"text-warning-600 bg-warning-50 dark:bg-warning-900/20\";\n        case \"low\":\n            return \"text-primary-600 bg-primary-50 dark:bg-primary-900/20\";\n        default:\n            return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n    }\n};\nfunction RecentAlerts() {\n    var _this = this;\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), alerts = _useState[0], setAlerts = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), loading = _useState1[0], setLoading = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_10__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), 2), error = _useState2[0], setError = _useState2[1];\n    var fetchAlerts = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_11__._)(function() {\n            var data, err, _err_response_data, _err_response;\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_12__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        setLoading(true);\n                        setError(null);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        return [\n                            4,\n                            _lib_api__WEBPACK_IMPORTED_MODULE_3__.alertsApi.getAlerts({\n                                limit: 4\n                            })\n                        ];\n                    case 2:\n                        data = _state.sent();\n                        setAlerts(data.alerts || []);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        err = _state.sent();\n                        setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || \"Failed to fetch alerts\");\n                        console.error(\"Error fetching alerts:\", err);\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        setLoading(false);\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function fetchAlerts() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        fetchAlerts();\n    }, []);\n    var handleRefresh = function() {\n        fetchAlerts();\n    };\n    var unreadCount = alerts.filter(function(alert) {\n        return !alert.is_read;\n    }).length;\n    if (loading && alerts.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                            children: \"Recent Alerts\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Recent Alerts\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: unreadCount\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRefresh,\n                                disabled: loading,\n                                className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50\",\n                                title: \"Refresh alerts\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                                children: \"View All\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-red-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 dark:text-red-400 mb-3\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleRefresh,\n                        disabled: loading,\n                        className: \"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50\",\n                        children: loading ? \"Retrying...\" : \"Try Again\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: alerts.map(function(alert) {\n                            var IconComponent = getAlertIcon(alert.type);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 rounded-lg border transition-colors cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 \".concat(alert.is_read ? \"border-gray-200 dark:border-gray-600 opacity-75\" : \"border-primary-200 dark:border-primary-700 bg-primary-50/30 dark:bg-primary-900/10\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-full \".concat(getSeverityColor(alert.severity)),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 23\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 21\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                                            children: alert.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 25\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-gray-500 dark:text-gray-400\",\n                                                                    children: alert.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 27\n                                                                }, _this),\n                                                                !alert.is_read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                                    lineNumber: 154,\n                                                                    columnNumber: 29\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 25\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 23\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: alert.message\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatTimeAgo)(alert.timestamp * 1000)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs px-2 py-1 rounded-full font-medium \".concat(alert.severity === \"high\" ? \"bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200\" : alert.severity === \"medium\" ? \"bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200\" : \"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"),\n                                                            children: alert.severity.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 25\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 23\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 21\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 19\n                                }, _this)\n                            }, alert.id, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, _this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    alerts.length === 0 && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_RefreshCw_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 dark:text-gray-400\",\n                                children: \"No recent alerts\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentAlerts, \"/vV0NdMX2EjB1COSECdOFKFU7M4=\");\n_c = RecentAlerts;\nvar _c;\n$RefreshReg$(_c, \"RecentAlerts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/RecentAlerts.tsx\n"));

/***/ })

});