"""
Application configuration settings
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import field_validator


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "Adaptive AI Sentinel"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # API
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    
    # CORS
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # Database
    DATABASE_URL: str = "sqlite:///./sentinela_ai.db"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_CACHE_TTL: int = 300  # 5 minutes
    
    # External APIs
    COINGECKO_API_URL: str = "https://api.coingecko.com/api/v3"
    COINGECKO_API_KEY: Optional[str] = None

    COINMARKETCAP_API_KEY: Optional[str] = None
    COINMARKETCAP_API_URL: str = "https://pro-api.coinmarketcap.com/v1"

    BINANCE_API_URL: str = "https://api.binance.com/api/v3"
    BINANCE_API_KEY: Optional[str] = None
    BINANCE_SECRET_KEY: Optional[str] = None

    ALPHA_VANTAGE_API_KEY: Optional[str] = None
    
    # OpenAI for AI explanations
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    OPENAI_MAX_TOKENS: int = 500
    
    # Market Data
    DEFAULT_CRYPTOCURRENCIES: List[str] = ["bitcoin", "ethereum", "binancecoin"]
    PRICE_UPDATE_INTERVAL: int = 60  # seconds
    ANALYSIS_UPDATE_INTERVAL: int = 300  # 5 minutes
    
    # AI/ML Settings
    MODEL_UPDATE_INTERVAL: int = 3600  # 1 hour
    CONFIDENCE_THRESHOLD: float = 0.6
    REGIME_DETECTION_WINDOW: int = 24  # hours
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 100
    
    # Background Tasks
    ENABLE_BACKGROUND_TASKS: bool = True
    TASK_QUEUE_URL: str = "redis://localhost:6379/1"
    
    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str]) -> str:
        if isinstance(v, str):
            return v
        # Fallback to environment variables
        return (
            f"postgresql://"
            f"{os.getenv('POSTGRES_USER', 'sentinela_user')}:"
            f"{os.getenv('POSTGRES_PASSWORD', 'sentinela_password')}@"
            f"{os.getenv('POSTGRES_HOST', 'localhost')}:"
            f"{os.getenv('POSTGRES_PORT', '5432')}/"
            f"{os.getenv('POSTGRES_DB', 'sentinela_ai')}"
        )
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()


# Development settings override
if settings.ENVIRONMENT == "development":
    settings.DEBUG = True
    settings.LOG_LEVEL = "DEBUG"
    settings.ALLOWED_HOSTS = ["*"]


# Production settings override
if settings.ENVIRONMENT == "production":
    settings.DEBUG = False
    settings.LOG_LEVEL = "INFO"
    # Override with secure settings in production
