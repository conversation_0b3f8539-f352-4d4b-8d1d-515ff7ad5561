"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/TrendingAssets.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/TrendingAssets.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrendingAssets: function() { return /* binding */ TrendingAssets; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_marketStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/marketStore */ \"(app-pages-browser)/./src/stores/marketStore.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ TrendingAssets auto */ \n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TrendingAssets() {\n    var _this = this;\n    _s();\n    var _useMarketStore = (0,_stores_marketStore__WEBPACK_IMPORTED_MODULE_3__.useMarketStore)(), trendingCurrencies = _useMarketStore.trendingCurrencies, loading = _useMarketStore.loading, fetchTrendingCurrencies = _useMarketStore.fetchTrendingCurrencies, trackCurrency = _useMarketStore.trackCurrency, untrackCurrency = _useMarketStore.untrackCurrency;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        fetchTrendingCurrencies(10);\n    }, [\n        fetchTrendingCurrencies\n    ]);\n    var handleWatchToggle = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_5__._)(function(symbol, isWatched) {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_6__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!isWatched) return [\n                            3,\n                            2\n                        ];\n                        return [\n                            4,\n                            untrackCurrency(symbol)\n                        ];\n                    case 1:\n                        _state.sent();\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        return [\n                            4,\n                            trackCurrency(symbol)\n                        ];\n                    case 3:\n                        _state.sent();\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function handleWatchToggle(symbol, isWatched) {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    var getSignalColor = function(signal) {\n        switch(signal){\n            case \"BUY\":\n                return \"text-success-600 bg-success-50 dark:bg-success-900/20\";\n            case \"SELL\":\n                return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n            case \"HOLD\":\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n            default:\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n        }\n    };\n    // Mock AI signals for demonstration (in production, this would come from the AI analysis)\n    var getAISignal = function(priceChange) {\n        if (priceChange > 5) return {\n            signal: \"BUY\",\n            confidence: 85\n        };\n        if (priceChange > 0) return {\n            signal: \"BUY\",\n            confidence: 65\n        };\n        if (priceChange > -5) return {\n            signal: \"HOLD\",\n            confidence: 50\n        };\n        return {\n            signal: \"SELL\",\n            confidence: 70\n        };\n    };\n    if (loading.isLoading && trendingCurrencies.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                            children: \"Trending Assets\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Trending Assets\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                            children: \"View All\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-b border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"24h Change\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"AI Signal\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: trendingCurrencies.length > 0 ? trendingCurrencies.map(function(asset, index) {\n                                var _asset_symbol;\n                                var aiSignal = getAISignal(asset.price_change_percentage_24h || 0);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: [\n                                                            asset.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: asset.image,\n                                                                alt: asset.name,\n                                                                className: \"w-6 h-6 rounded-full\",\n                                                                onError: function(e) {\n                                                                    e.currentTarget.style.display = \"none\";\n                                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 27\n                                                            }, _this) : null,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                style: {\n                                                                    display: asset.image ? \"none\" : \"flex\"\n                                                                },\n                                                                children: (_asset_symbol = asset.symbol) === null || _asset_symbol === void 0 ? void 0 : _asset_symbol.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 25\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                children: asset.symbol\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 25\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: asset.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 25\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 21\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: asset.current_price ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(asset.current_price) : \"--\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: asset.market_cap ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(asset.market_cap) : \"--\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-end space-x-1 \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)(asset.price_change_percentage_24h || 0)),\n                                                children: [\n                                                    (asset.price_change_percentage_24h || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 25\n                                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 25\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            (asset.price_change_percentage_24h || 0) > 0 ? \"+\" : \"\",\n                                                            asset.price_change_percentage_24h ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(asset.price_change_percentage_24h) : \"--\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 dark:text-white\",\n                                                children: asset.total_volume ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(asset.total_volume) : \"--\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 21\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getSignalColor(aiSignal.signal)),\n                                                        children: aiSignal.signal\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            aiSignal.confidence,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 23\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: function() {\n                                                            return handleWatchToggle(asset.symbol, asset.is_tracked || false);\n                                                        },\n                                                        className: \"p-1 rounded transition-colors \".concat(asset.is_tracked ? \"text-warning-500 hover:text-warning-600\" : \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"),\n                                                        title: asset.is_tracked ? \"Remove from watchlist\" : \"Add to watchlist\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(asset.is_tracked ? \"fill-current\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 25\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-1 rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\",\n                                                        title: \"View details\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 25\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 23\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, _this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, _this);\n                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    colSpan: 6,\n                                    className: \"py-8 text-center text-gray-500 dark:text-gray-400\",\n                                    children: \"No trending assets available\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(TrendingAssets, \"mpW+07xTi3L6/aluG9sVUFaJrB4=\", false, function() {\n    return [\n        _stores_marketStore__WEBPACK_IMPORTED_MODULE_3__.useMarketStore\n    ];\n});\n_c = TrendingAssets;\nvar _c;\n$RefreshReg$(_c, \"TrendingAssets\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TrendingAssets.tsx\n"));

/***/ })

});