'use client';

import { useEffect, useState } from 'react';
import { Bell, TrendingUp, AlertTriangle, Target, Clock, RefreshCw } from 'lucide-react';
import { formatTimeAgo } from '@/lib/utils';
import { Alert } from '@/types';
import { alertsApi } from '@/lib/api';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

const getAlertIcon = (type: string) => {
  switch (type) {
    case 'pattern_detected':
      return TrendingUp;
    case 'regime_change':
      return AlertTriangle;
    case 'signal_change':
      return Target;
    case 'price_level':
      return Clock;
    default:
      return Bell;
  }
};

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'high':
      return 'text-danger-600 bg-danger-50 dark:bg-danger-900/20';
    case 'medium':
      return 'text-warning-600 bg-warning-50 dark:bg-warning-900/20';
    case 'low':
      return 'text-primary-600 bg-primary-50 dark:bg-primary-900/20';
    default:
      return 'text-gray-600 bg-gray-50 dark:bg-gray-700';
  }
};

export function RecentAlerts() {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAlerts = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await alertsApi.getAlerts({ limit: 4 });
      setAlerts(data.alerts || []);
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to fetch alerts');
      console.error('Error fetching alerts:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAlerts();
  }, []);

  const handleRefresh = () => {
    fetchAlerts();
  };

  const unreadCount = Array.isArray(alerts) ? alerts.filter(alert => !alert.is_read).length : 0;

  if (loading && (!Array.isArray(alerts) || alerts.length === 0)) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Bell className="h-6 w-6 text-primary-600 dark:text-primary-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Recent Alerts
          </h2>
        </div>
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Bell className="h-6 w-6 text-primary-600 dark:text-primary-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Recent Alerts
          </h2>
          {unreadCount > 0 && (
            <span className="bg-danger-500 text-white text-xs px-2 py-1 rounded-full">
              {unreadCount}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
            title="Refresh alerts"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <button className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
            View All
          </button>
        </div>
      </div>

      {error ? (
        <div className="text-center py-8">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 dark:text-red-400 mb-3">{error}</p>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Retrying...' : 'Try Again'}
          </button>
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {Array.isArray(alerts) && alerts.map((alert) => {
              const IconComponent = getAlertIcon(alert.type);

              return (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border transition-colors cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    alert.is_read
                      ? 'border-gray-200 dark:border-gray-600 opacity-75'
                      : 'border-primary-200 dark:border-primary-700 bg-primary-50/30 dark:bg-primary-900/10'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-full ${getSeverityColor(alert.severity)}`}>
                      <IconComponent className="h-4 w-4" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {alert.title}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                            {alert.symbol}
                          </span>
                          {!alert.is_read && (
                            <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                          )}
                        </div>
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {alert.message}
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatTimeAgo(alert.timestamp * 1000)}
                        </span>

                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                          alert.severity === 'high' ? 'bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200' :
                          alert.severity === 'medium' ? 'bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200' :
                          'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200'
                        }`}>
                          {alert.severity.toUpperCase()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {(!Array.isArray(alerts) || alerts.length === 0) && !loading && (
            <div className="text-center py-8">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">No recent alerts</p>
            </div>
          )}
        </>
      )}
    </div>
  );
}
