"""
AI Analysis API endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from app.core.database import get_db
from app.services.ai_analysis_service import AIAnalysisService

router = APIRouter()


@router.get("/ai-insights/{symbol}")
async def get_ai_insights(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Get AI insights for a specific cryptocurrency"""
    try:
        ai_service = AIAnalysisService(db)
        insights = await ai_service.generate_ai_insights(symbol.upper())

        return {
            "success": True,
            "data": insights
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market-regime/{symbol}")
async def get_market_regime(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Get current market regime for a cryptocurrency"""
    try:
        ai_service = AIAnalysisService(db)
        regime = await ai_service.detect_market_regime(symbol.upper())

        return {
            "success": True,
            "data": regime
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/technical-indicators/{symbol}")
async def get_technical_indicators(
    symbol: str,
    indicators: Optional[str] = Query(None, description="Comma-separated list of indicators"),
    db: Session = Depends(get_db)
):
    """Get technical indicators for a cryptocurrency"""
    try:
        ai_service = AIAnalysisService(db)
        tech_indicators = await ai_service.get_technical_indicators(symbol.upper())

        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "indicators": tech_indicators,
                "timestamp": int(datetime.now().timestamp())
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chart-patterns/{symbol}")
async def get_chart_patterns(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Get detected chart patterns for a cryptocurrency"""
    try:
        ai_service = AIAnalysisService(db)
        patterns = await ai_service.detect_chart_patterns(symbol.upper())

        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "patterns": patterns,
                "timestamp": int(datetime.now().timestamp())
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
