/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/combined-stream";
exports.ids = ["vendor-chunks/combined-stream"];
exports.modules = {

/***/ "(ssr)/../node_modules/combined-stream/lib/combined_stream.js":
/*!**************************************************************!*\
  !*** ../node_modules/combined-stream/lib/combined_stream.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var util = __webpack_require__(/*! util */ \"util\");\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\nvar DelayedStream = __webpack_require__(/*! delayed-stream */ \"(ssr)/../node_modules/delayed-stream/lib/delayed_stream.js\");\nmodule.exports = CombinedStream;\nfunction CombinedStream() {\n    this.writable = false;\n    this.readable = true;\n    this.dataSize = 0;\n    this.maxDataSize = 2 * 1024 * 1024;\n    this.pauseStreams = true;\n    this._released = false;\n    this._streams = [];\n    this._currentStream = null;\n    this._insideLoop = false;\n    this._pendingNext = false;\n}\nutil.inherits(CombinedStream, Stream);\nCombinedStream.create = function(options) {\n    var combinedStream = new this();\n    options = options || {};\n    for(var option in options){\n        combinedStream[option] = options[option];\n    }\n    return combinedStream;\n};\nCombinedStream.isStreamLike = function(stream) {\n    return typeof stream !== \"function\" && typeof stream !== \"string\" && typeof stream !== \"boolean\" && typeof stream !== \"number\" && !Buffer.isBuffer(stream);\n};\nCombinedStream.prototype.append = function(stream) {\n    var isStreamLike = CombinedStream.isStreamLike(stream);\n    if (isStreamLike) {\n        if (!(stream instanceof DelayedStream)) {\n            var newStream = DelayedStream.create(stream, {\n                maxDataSize: Infinity,\n                pauseStream: this.pauseStreams\n            });\n            stream.on(\"data\", this._checkDataSize.bind(this));\n            stream = newStream;\n        }\n        this._handleErrors(stream);\n        if (this.pauseStreams) {\n            stream.pause();\n        }\n    }\n    this._streams.push(stream);\n    return this;\n};\nCombinedStream.prototype.pipe = function(dest, options) {\n    Stream.prototype.pipe.call(this, dest, options);\n    this.resume();\n    return dest;\n};\nCombinedStream.prototype._getNext = function() {\n    this._currentStream = null;\n    if (this._insideLoop) {\n        this._pendingNext = true;\n        return; // defer call\n    }\n    this._insideLoop = true;\n    try {\n        do {\n            this._pendingNext = false;\n            this._realGetNext();\n        }while (this._pendingNext);\n    } finally{\n        this._insideLoop = false;\n    }\n};\nCombinedStream.prototype._realGetNext = function() {\n    var stream = this._streams.shift();\n    if (typeof stream == \"undefined\") {\n        this.end();\n        return;\n    }\n    if (typeof stream !== \"function\") {\n        this._pipeNext(stream);\n        return;\n    }\n    var getStream = stream;\n    getStream((function(stream) {\n        var isStreamLike = CombinedStream.isStreamLike(stream);\n        if (isStreamLike) {\n            stream.on(\"data\", this._checkDataSize.bind(this));\n            this._handleErrors(stream);\n        }\n        this._pipeNext(stream);\n    }).bind(this));\n};\nCombinedStream.prototype._pipeNext = function(stream) {\n    this._currentStream = stream;\n    var isStreamLike = CombinedStream.isStreamLike(stream);\n    if (isStreamLike) {\n        stream.on(\"end\", this._getNext.bind(this));\n        stream.pipe(this, {\n            end: false\n        });\n        return;\n    }\n    var value = stream;\n    this.write(value);\n    this._getNext();\n};\nCombinedStream.prototype._handleErrors = function(stream) {\n    var self = this;\n    stream.on(\"error\", function(err) {\n        self._emitError(err);\n    });\n};\nCombinedStream.prototype.write = function(data) {\n    this.emit(\"data\", data);\n};\nCombinedStream.prototype.pause = function() {\n    if (!this.pauseStreams) {\n        return;\n    }\n    if (this.pauseStreams && this._currentStream && typeof this._currentStream.pause == \"function\") this._currentStream.pause();\n    this.emit(\"pause\");\n};\nCombinedStream.prototype.resume = function() {\n    if (!this._released) {\n        this._released = true;\n        this.writable = true;\n        this._getNext();\n    }\n    if (this.pauseStreams && this._currentStream && typeof this._currentStream.resume == \"function\") this._currentStream.resume();\n    this.emit(\"resume\");\n};\nCombinedStream.prototype.end = function() {\n    this._reset();\n    this.emit(\"end\");\n};\nCombinedStream.prototype.destroy = function() {\n    this._reset();\n    this.emit(\"close\");\n};\nCombinedStream.prototype._reset = function() {\n    this.writable = false;\n    this._streams = [];\n    this._currentStream = null;\n};\nCombinedStream.prototype._checkDataSize = function() {\n    this._updateDataSize();\n    if (this.dataSize <= this.maxDataSize) {\n        return;\n    }\n    var message = \"DelayedStream#maxDataSize of \" + this.maxDataSize + \" bytes exceeded.\";\n    this._emitError(new Error(message));\n};\nCombinedStream.prototype._updateDataSize = function() {\n    this.dataSize = 0;\n    var self = this;\n    this._streams.forEach(function(stream) {\n        if (!stream.dataSize) {\n            return;\n        }\n        self.dataSize += stream.dataSize;\n    });\n    if (this._currentStream && this._currentStream.dataSize) {\n        this.dataSize += this._currentStream.dataSize;\n    }\n};\nCombinedStream.prototype._emitError = function(err) {\n    this._reset();\n    this.emit(\"error\", err);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/combined-stream/lib/combined_stream.js\n");

/***/ })

};
;