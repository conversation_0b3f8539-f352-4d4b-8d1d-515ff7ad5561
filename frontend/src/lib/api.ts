/**
 * API client for the Adaptive AI Sentinel backend
 */

import axios from 'axios';
import { CryptoCurrency, AIAnalysis, Alert, ChartData } from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Market Data API
export const marketApi = {
  // Get list of cryptocurrencies
  getCryptocurrencies: async (params?: {
    limit?: number;
    offset?: number;
    search?: string;
  }): Promise<CryptoCurrency[]> => {
    const response = await apiClient.get('/market/cryptocurrencies', { params });
    return response.data.data;
  },

  // Get specific cryptocurrency
  getCryptocurrency: async (symbol: string): Promise<CryptoCurrency> => {
    const response = await apiClient.get(`/market/cryptocurrencies/${symbol}`);
    return response.data.data;
  },

  // Get price history
  getPriceHistory: async (
    symbol: string,
    timeframe: string = '1d',
    days: number = 30
  ): Promise<ChartData[]> => {
    const response = await apiClient.get(`/market/cryptocurrencies/${symbol}/price-history`, {
      params: { timeframe, days }
    });
    return response.data.data;
  },

  // Get market overview
  getMarketOverview: async (): Promise<any> => {
    const response = await apiClient.get('/market/market-overview');
    return response.data.data;
  },

  // Get trending cryptocurrencies
  getTrending: async (limit: number = 10): Promise<CryptoCurrency[]> => {
    const response = await apiClient.get('/market/trending', {
      params: { limit }
    });
    return response.data.data;
  },

  // Track cryptocurrency
  trackCryptocurrency: async (symbol: string): Promise<any> => {
    const response = await apiClient.post(`/market/cryptocurrencies/${symbol}/track`);
    return response.data;
  },

  // Untrack cryptocurrency
  untrackCryptocurrency: async (symbol: string): Promise<any> => {
    const response = await apiClient.delete(`/market/cryptocurrencies/${symbol}/track`);
    return response.data;
  },

  // Refresh market data
  refreshMarketData: async (): Promise<any> => {
    const response = await apiClient.post('/market/refresh');
    return response.data;
  },
};

// AI Analysis API
export const analysisApi = {
  // Get AI insights for a cryptocurrency
  getAIInsights: async (symbol: string): Promise<AIAnalysis> => {
    const response = await apiClient.get(`/analysis/ai-insights/${symbol}`);
    return response.data.data;
  },

  // Get market regime
  getMarketRegime: async (symbol: string): Promise<any> => {
    const response = await apiClient.get(`/analysis/market-regime/${symbol}`);
    return response.data.data;
  },

  // Get technical indicators
  getTechnicalIndicators: async (symbol: string, indicators?: string): Promise<any> => {
    const response = await apiClient.get(`/analysis/technical-indicators/${symbol}`, {
      params: indicators ? { indicators } : {}
    });
    return response.data.data;
  },

  // Get chart patterns
  getChartPatterns: async (symbol: string): Promise<any> => {
    const response = await apiClient.get(`/analysis/chart-patterns/${symbol}`);
    return response.data.data;
  },
};

// Alerts API
export const alertsApi = {
  // Get alerts
  getAlerts: async (params?: {
    limit?: number;
    offset?: number;
    severity?: string;
    unread_only?: boolean;
  }): Promise<{ alerts: Alert[]; total: number; unread_count: number }> => {
    const response = await apiClient.get('/alerts', { params });
    return {
      alerts: response.data.data,
      total: response.data.total,
      unread_count: response.data.unread_count,
    };
  },

  // Get specific alert
  getAlert: async (alertId: string): Promise<Alert> => {
    const response = await apiClient.get(`/alerts/${alertId}`);
    return response.data.data;
  },

  // Mark alert as read
  markAsRead: async (alertId: string): Promise<any> => {
    const response = await apiClient.patch(`/alerts/${alertId}/read`);
    return response.data;
  },

  // Mark all alerts as read
  markAllAsRead: async (): Promise<any> => {
    const response = await apiClient.patch('/alerts/mark-all-read');
    return response.data;
  },

  // Delete alert
  deleteAlert: async (alertId: string): Promise<any> => {
    const response = await apiClient.delete(`/alerts/${alertId}`);
    return response.data;
  },

  // Get alert statistics
  getAlertStats: async (): Promise<any> => {
    const response = await apiClient.get('/alerts/stats/summary');
    return response.data.data;
  },
};

// Health API
export const healthApi = {
  // Basic health check
  getHealth: async (): Promise<any> => {
    const response = await apiClient.get('/health');
    return response.data;
  },

  // Detailed health check
  getDetailedHealth: async (): Promise<any> => {
    const response = await apiClient.get('/health/detailed');
    return response.data;
  },
};

// Export default API client for custom requests
export default apiClient;
