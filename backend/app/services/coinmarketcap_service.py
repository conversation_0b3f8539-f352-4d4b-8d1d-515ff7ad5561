"""
CoinMarketCap API service
"""

import httpx
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from app.core.config import settings
from loguru import logger


class CoinMarketCapService:
    """Service for handling CoinMarketCap API operations"""
    
    def __init__(self, db: Session):
        self.db = db
        self.base_url = "https://pro-api.coinmarketcap.com/v1"
        self.sandbox_url = "https://sandbox-api.coinmarketcap.com/v1"
        
        # Use sandbox for development if no API key
        self.api_url = self.sandbox_url if not settings.COINMARKETCAP_API_KEY else self.base_url
        
        self.headers = {
            "Accept": "application/json",
            "Accept-Encoding": "deflate, gzip"
        }
        
        if settings.COINMARKETCAP_API_KEY:
            self.headers["X-CMC_PRO_API_KEY"] = settings.COINMARKETCAP_API_KEY
    
    async def get_cryptocurrencies(
        self, 
        limit: int = 100, 
        start: int = 1,
        convert: str = "USD"
    ) -> List[Dict]:
        """Get cryptocurrency listings from CoinMarketCap"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                url = f"{self.api_url}/cryptocurrency/listings/latest"
                
                params = {
                    "start": start,
                    "limit": min(limit, 5000),  # CMC max is 5000
                    "convert": convert,
                    "sort": "market_cap",
                    "sort_dir": "desc"
                }
                
                logger.info(f"Fetching cryptocurrencies from CoinMarketCap: {url}")
                
                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()
                
                data = response.json()
                
                if data.get("status", {}).get("error_code") != 0:
                    error_message = data.get("status", {}).get("error_message", "Unknown error")
                    raise Exception(f"CoinMarketCap API error: {error_message}")
                
                cryptocurrencies = []
                for crypto in data.get("data", []):
                    quote_data = crypto.get("quote", {}).get(convert, {})
                    
                    cryptocurrencies.append({
                        "id": str(crypto.get("id")),
                        "symbol": crypto.get("symbol"),
                        "name": crypto.get("name"),
                        "slug": crypto.get("slug"),
                        "current_price": quote_data.get("price"),
                        "market_cap": quote_data.get("market_cap"),
                        "market_cap_rank": crypto.get("cmc_rank"),
                        "fully_diluted_market_cap": quote_data.get("fully_diluted_market_cap"),
                        "total_volume": quote_data.get("volume_24h"),
                        "price_change_24h": quote_data.get("price_change_24h"),
                        "price_change_percentage_24h": quote_data.get("percent_change_24h"),
                        "price_change_percentage_7d": quote_data.get("percent_change_7d"),
                        "price_change_percentage_30d": quote_data.get("percent_change_30d"),
                        "circulating_supply": crypto.get("circulating_supply"),
                        "total_supply": crypto.get("total_supply"),
                        "max_supply": crypto.get("max_supply"),
                        "last_updated": crypto.get("last_updated"),
                        "date_added": crypto.get("date_added"),
                        "tags": crypto.get("tags", []),
                        "platform": crypto.get("platform"),
                        "market_cap_dominance": quote_data.get("market_cap_dominance")
                    })
                
                logger.info(f"Successfully fetched {len(cryptocurrencies)} cryptocurrencies from CoinMarketCap")
                return cryptocurrencies
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching from CoinMarketCap: {e.response.status_code} - {e.response.text}")
            raise Exception(f"CoinMarketCap API error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error fetching cryptocurrencies from CoinMarketCap: {str(e)}")
            raise Exception(f"Error fetching cryptocurrencies: {str(e)}")
    
    async def get_cryptocurrency_by_symbol(self, symbol: str) -> Optional[Dict]:
        """Get specific cryptocurrency by symbol"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                url = f"{self.api_url}/cryptocurrency/quotes/latest"
                
                params = {
                    "symbol": symbol.upper(),
                    "convert": "USD"
                }
                
                logger.info(f"Fetching {symbol} from CoinMarketCap")
                
                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()
                
                data = response.json()
                
                if data.get("status", {}).get("error_code") != 0:
                    return None
                
                crypto_data = data.get("data", {}).get(symbol.upper())
                if not crypto_data:
                    return None
                
                quote_data = crypto_data.get("quote", {}).get("USD", {})
                
                return {
                    "id": str(crypto_data.get("id")),
                    "symbol": crypto_data.get("symbol"),
                    "name": crypto_data.get("name"),
                    "slug": crypto_data.get("slug"),
                    "current_price": quote_data.get("price"),
                    "market_cap": quote_data.get("market_cap"),
                    "market_cap_rank": crypto_data.get("cmc_rank"),
                    "fully_diluted_market_cap": quote_data.get("fully_diluted_market_cap"),
                    "total_volume": quote_data.get("volume_24h"),
                    "price_change_24h": quote_data.get("price_change_24h"),
                    "price_change_percentage_24h": quote_data.get("percent_change_24h"),
                    "price_change_percentage_7d": quote_data.get("percent_change_7d"),
                    "circulating_supply": crypto_data.get("circulating_supply"),
                    "total_supply": crypto_data.get("total_supply"),
                    "max_supply": crypto_data.get("max_supply"),
                    "last_updated": crypto_data.get("last_updated"),
                    "date_added": crypto_data.get("date_added"),
                    "tags": crypto_data.get("tags", [])
                }
                
        except Exception as e:
            logger.error(f"Error fetching {symbol} from CoinMarketCap: {str(e)}")
            return None
    
    async def get_global_metrics(self) -> Dict:
        """Get global cryptocurrency market metrics"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                url = f"{self.api_url}/global-metrics/quotes/latest"
                
                params = {
                    "convert": "USD"
                }
                
                logger.info("Fetching global metrics from CoinMarketCap")
                
                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()
                
                data = response.json()
                
                if data.get("status", {}).get("error_code") != 0:
                    error_message = data.get("status", {}).get("error_message", "Unknown error")
                    raise Exception(f"CoinMarketCap API error: {error_message}")
                
                global_data = data.get("data", {})
                quote_data = global_data.get("quote", {}).get("USD", {})
                
                return {
                    "total_market_cap": quote_data.get("total_market_cap"),
                    "total_volume": quote_data.get("total_volume_24h"),
                    "market_cap_change_24h": quote_data.get("total_market_cap_yesterday_percentage_change"),
                    "volume_change_24h": quote_data.get("total_volume_24h_yesterday_percentage_change"),
                    "btc_dominance": global_data.get("btc_dominance"),
                    "eth_dominance": global_data.get("eth_dominance"),
                    "active_cryptocurrencies": global_data.get("active_cryptocurrencies"),
                    "active_exchanges": global_data.get("active_exchanges"),
                    "active_market_pairs": global_data.get("active_market_pairs"),
                    "last_updated": global_data.get("last_updated")
                }
                
        except Exception as e:
            logger.error(f"Error fetching global metrics from CoinMarketCap: {str(e)}")
            raise Exception(f"Error fetching global metrics: {str(e)}")
    
    async def get_trending_cryptocurrencies(self, limit: int = 10) -> List[Dict]:
        """Get trending cryptocurrencies (top gainers)"""
        try:
            # Get top cryptocurrencies and sort by 24h change
            cryptocurrencies = await self.get_cryptocurrencies(limit=100)
            
            # Sort by 24h percentage change (trending up)
            trending = sorted(
                cryptocurrencies, 
                key=lambda x: x.get("price_change_percentage_24h", 0), 
                reverse=True
            )
            
            return trending[:limit]
            
        except Exception as e:
            logger.error(f"Error fetching trending cryptocurrencies from CoinMarketCap: {str(e)}")
            raise Exception(f"Error fetching trending cryptocurrencies: {str(e)}")
