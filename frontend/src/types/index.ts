// Market Data Types
export interface CryptoCurrency {
  id: string;
  symbol: string;
  name: string;
  image: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

export interface PriceData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface ChartData {
  time: number;
  value: number;
}

// Market Regime Types
export enum MarketRegime {
  TRENDING_UP = 'trending_up',
  TRENDING_DOWN = 'trending_down',
  RANGING = 'ranging',
  VOLATILE = 'volatile',
  CONSOLIDATION = 'consolidation',
  BREAKOUT = 'breakout',
}

export interface MarketRegimeData {
  regime: MarketRegime;
  confidence: number;
  description: string;
  indicators_used: string[];
  timestamp: number;
  duration_hours: number;
}

// Technical Analysis Types
export interface TechnicalIndicator {
  name: string;
  value: number;
  signal: 'BUY' | 'SELL' | 'NEUTRAL';
  confidence: number;
  description: string;
  parameters: Record<string, any>;
}

export interface SupportResistance {
  level: number;
  type: 'support' | 'resistance';
  strength: number;
  touches: number;
  last_touch: number;
}

export interface ChartPattern {
  name: string;
  type: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  description: string;
  target_price?: number;
  stop_loss?: number;
  formation_start: number;
  formation_end: number;
  breakout_price?: number;
}

// AI Analysis Types
export interface AIAnalysis {
  symbol: string;
  timestamp: number;
  market_regime: {
    regime: string;
    confidence: number;
    description: string;
    indicators_used: string[];
    duration_hours: number;
    characteristics?: Record<string, number>;
  };
  technical_indicators: TechnicalIndicator[];
  chart_patterns: ChartPattern[];
  overall_signal: 'BUY' | 'SELL' | 'HOLD';
  confidence_score: number;
  explanation: string;
  key_levels?: {
    support?: number;
    resistance?: number;
    target?: number;
    stop_loss?: number;
  };
  analysis_version?: string;
  model_confidence?: string;
}

// Alert Types
export interface Alert {
  id: string;
  symbol: string;
  type: 'regime_change' | 'pattern_detected' | 'signal_change' | 'price_level';
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  timestamp: number;
  is_read: boolean;
  data?: Record<string, any>;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface ChartSettings {
  timeframe: '1h' | '4h' | '1d' | '1w';
  indicators: string[];
  show_patterns: boolean;
  show_support_resistance: boolean;
  chart_type: 'candlestick' | 'line' | 'area';
}

// Store Types
export interface MarketStore {
  currencies: CryptoCurrency[];
  selectedCurrency: string;
  priceData: Record<string, PriceData[]>;
  aiAnalysis: Record<string, AIAnalysis>;
  loading: LoadingState;
  fetchCurrencies: () => Promise<void>;
  fetchPriceData: (symbol: string, timeframe: string) => Promise<void>;
  fetchAIAnalysis: (symbol: string) => Promise<void>;
  setSelectedCurrency: (symbol: string) => void;
}

export interface AlertStore {
  alerts: Alert[];
  unreadCount: number;
  fetchAlerts: () => Promise<void>;
  markAsRead: (alertId: string) => void;
  markAllAsRead: () => void;
  deleteAlert: (alertId: string) => void;
}

export interface SettingsStore {
  chartSettings: ChartSettings;
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    push: boolean;
    regime_changes: boolean;
    high_confidence_signals: boolean;
  };
  updateChartSettings: (settings: Partial<ChartSettings>) => void;
  updateNotifications: (notifications: Partial<SettingsStore['notifications']>) => void;
}
