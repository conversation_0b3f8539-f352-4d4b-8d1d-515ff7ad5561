"""
Alerts API endpoints
"""

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.core.database import get_db
from app.services.alert_service import get_alert_service

router = APIRouter()


@router.get("/")
async def get_alerts(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    severity: Optional[str] = Query(None, regex="^(low|medium|high)$"),
    unread_only: bool = Query(False),
    db: Session = Depends(get_db)
):
    """Get list of alerts"""
    try:
        alert_service = get_alert_service(db)

        # Get alerts from the service
        alerts = await alert_service.get_alerts(
            limit=limit,
            offset=offset,
            unread_only=unread_only
        )

        # Apply severity filter if provided
        if severity:
            alerts = [alert for alert in alerts if alert["severity"] == severity]

        # Calculate unread count
        all_alerts = await alert_service.get_alerts(limit=1000, offset=0)
        unread_count = len([alert for alert in all_alerts if not alert["is_read"]])

        return {
            "success": True,
            "data": alerts,
            "total": len(alerts),
            "limit": limit,
            "offset": offset,
            "unread_count": unread_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{alert_id}")
async def get_alert(
    alert_id: str,
    db: Session = Depends(get_db)
):
    """Get specific alert by ID"""
    try:
        # Mock single alert data
        mock_alert = {
            "id": alert_id,
            "symbol": "BTC",
            "type": "pattern_detected",
            "title": "Bullish Flag Pattern Detected",
            "message": "BTC showing strong bullish flag formation with 78% confidence",
            "severity": "high",
            "timestamp": 1703123456,
            "is_read": False,
            "data": {
                "pattern": "bullish_flag",
                "confidence": 78,
                "target_price": 48500,
                "stop_loss": 41000,
                "formation_start": 1703000000,
                "formation_end": 1703123456
            }
        }
        
        return {
            "success": True,
            "data": mock_alert
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.patch("/{alert_id}/read")
async def mark_alert_as_read(
    alert_id: str,
    db: Session = Depends(get_db)
):
    """Mark alert as read"""
    try:
        alert_service = get_alert_service(db)
        await alert_service.mark_alert_as_read(alert_id)

        return {
            "success": True,
            "message": f"Alert {alert_id} marked as read",
            "data": {"id": alert_id, "is_read": True}
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.patch("/mark-all-read")
async def mark_all_alerts_as_read(db: Session = Depends(get_db)):
    """Mark all alerts as read"""
    try:
        # In production, this would update all unread alerts in the database
        return {
            "success": True,
            "message": "All alerts marked as read",
            "data": {"marked_count": 3}  # Mock count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{alert_id}")
async def delete_alert(
    alert_id: str,
    db: Session = Depends(get_db)
):
    """Delete an alert"""
    try:
        alert_service = get_alert_service(db)
        await alert_service.delete_alert(alert_id)

        return {
            "success": True,
            "message": f"Alert {alert_id} deleted",
            "data": {"id": alert_id, "deleted": True}
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/summary")
async def get_alert_stats(db: Session = Depends(get_db)):
    """Get alert statistics summary"""
    try:
        # Mock alert statistics
        stats = {
            "total_alerts": 15,
            "unread_alerts": 3,
            "alerts_today": 5,
            "alerts_this_week": 12,
            "by_severity": {
                "high": 2,
                "medium": 8,
                "low": 5
            },
            "by_type": {
                "pattern_detected": 6,
                "regime_change": 4,
                "signal_change": 3,
                "price_level": 2
            }
        }
        
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate/{symbol}")
async def generate_alerts_for_symbol(
    symbol: str,
    db: Session = Depends(get_db)
):
    """Generate alerts for a specific cryptocurrency"""
    try:
        alert_service = get_alert_service(db)
        alerts = await alert_service.generate_alerts_for_symbol(symbol.upper())

        return {
            "success": True,
            "message": f"Generated {len(alerts)} alerts for {symbol.upper()}",
            "data": alerts
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate-batch")
async def generate_alerts_batch(
    symbols: List[str],
    db: Session = Depends(get_db)
):
    """Generate alerts for multiple cryptocurrencies"""
    try:
        alert_service = get_alert_service(db)
        all_alerts = await alert_service.generate_alerts_for_watchlist([s.upper() for s in symbols])

        return {
            "success": True,
            "message": f"Generated {len(all_alerts)} alerts for {len(symbols)} symbols",
            "data": all_alerts
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
