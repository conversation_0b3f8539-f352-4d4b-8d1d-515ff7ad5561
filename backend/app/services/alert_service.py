"""
Alert service for generating and managing real-time alerts
"""

import uuid
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from app.core.database import get_db
from app.models.alerts import Alert
from app.services.ai_analysis_service import AIAnalysisService
from app.services.unified_market_service import UnifiedMarketService
from loguru import logger


class AlertService:
    """Service for managing cryptocurrency alerts"""
    
    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIAnalysisService(db)
        self.market_service = UnifiedMarketService(db)
        
    async def generate_alerts_for_symbol(self, symbol: str) -> List[Dict]:
        """Generate alerts for a specific cryptocurrency"""
        alerts = []
        
        try:
            # Get current AI analysis
            ai_insights = await self.ai_service.generate_ai_insights(symbol)
            
            # Check for regime changes
            regime_alert = await self._check_regime_change(symbol, ai_insights['market_regime'])
            if regime_alert:
                alerts.append(regime_alert)
            
            # Check for pattern detections
            pattern_alerts = await self._check_pattern_detections(symbol, ai_insights['chart_patterns'])
            alerts.extend(pattern_alerts)
            
            # Check for signal changes
            signal_alert = await self._check_signal_change(symbol, ai_insights['overall_signal'], ai_insights['confidence_score'])
            if signal_alert:
                alerts.append(signal_alert)
            
            # Check for key level approaches
            level_alerts = await self._check_key_level_approaches(symbol, ai_insights.get('key_levels', {}))
            alerts.extend(level_alerts)
            
            # Save alerts to database
            for alert_data in alerts:
                await self._save_alert(alert_data)
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error generating alerts for {symbol}: {e}")
            return []
    
    async def _check_regime_change(self, symbol: str, current_regime: Dict) -> Optional[Dict]:
        """Check if market regime has changed"""
        try:
            # Get the last regime from database (simplified - would use actual storage)
            # For now, we'll simulate regime change detection
            
            regime = current_regime.get('regime', 'unknown')
            confidence = current_regime.get('confidence', 0)
            
            # Only alert on high-confidence regime changes
            if confidence > 70:
                return {
                    'id': str(uuid.uuid4()),
                    'symbol': symbol,
                    'type': 'regime_change',
                    'title': 'Market Regime Change',
                    'message': f'{symbol} market regime changed to {regime.replace("_", " ").title()} (confidence: {confidence}%)',
                    'severity': 'medium' if confidence > 80 else 'low',
                    'timestamp': int(datetime.now().timestamp()),
                    'is_read': False,
                    'data': {
                        'regime': regime,
                        'confidence': confidence,
                        'description': current_regime.get('description', '')
                    }
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking regime change for {symbol}: {e}")
            return None
    
    async def _check_pattern_detections(self, symbol: str, patterns: List[Dict]) -> List[Dict]:
        """Check for new chart pattern detections"""
        alerts = []
        
        try:
            for pattern in patterns:
                confidence = pattern.get('confidence', 0)
                pattern_type = pattern.get('type', '')
                pattern_name = pattern.get('name', '')
                
                # Only alert on high-confidence patterns
                if confidence > 65:
                    severity = 'high' if confidence > 80 else 'medium'
                    
                    alert = {
                        'id': str(uuid.uuid4()),
                        'symbol': symbol,
                        'type': 'pattern_detected',
                        'title': f'{pattern_name} Pattern Detected',
                        'message': f'{symbol} showing {pattern_name.lower()} formation with {confidence}% confidence',
                        'severity': severity,
                        'timestamp': int(datetime.now().timestamp()),
                        'is_read': False,
                        'data': {
                            'pattern_name': pattern_name,
                            'pattern_type': pattern_type,
                            'confidence': confidence,
                            'description': pattern.get('description', ''),
                            'target_price': pattern.get('target_price'),
                            'stop_loss': pattern.get('stop_loss')
                        }
                    }
                    alerts.append(alert)
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error checking pattern detections for {symbol}: {e}")
            return []
    
    async def _check_signal_change(self, symbol: str, current_signal: str, confidence: int) -> Optional[Dict]:
        """Check if AI signal has changed"""
        try:
            # Only alert on high-confidence signal changes
            if confidence > 70:
                severity = 'high' if confidence > 85 else 'medium'
                
                return {
                    'id': str(uuid.uuid4()),
                    'symbol': symbol,
                    'type': 'signal_change',
                    'title': 'AI Signal Update',
                    'message': f'{symbol} signal changed to {current_signal} (confidence: {confidence}%)',
                    'severity': severity,
                    'timestamp': int(datetime.now().timestamp()),
                    'is_read': False,
                    'data': {
                        'signal': current_signal,
                        'confidence': confidence
                    }
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking signal change for {symbol}: {e}")
            return None
    
    async def _check_key_level_approaches(self, symbol: str, key_levels: Dict) -> List[Dict]:
        """Check if price is approaching key levels"""
        alerts = []
        
        try:
            # Get current price
            crypto_data = await self.market_service.get_cryptocurrency_by_symbol(symbol)
            if not crypto_data:
                return alerts
            
            current_price = crypto_data.get('current_price', 0)
            if not current_price:
                return alerts
            
            # Check support level
            support = key_levels.get('support')
            if support:
                distance_to_support = abs(current_price - support) / support
                if distance_to_support < 0.02:  # Within 2%
                    alerts.append({
                        'id': str(uuid.uuid4()),
                        'symbol': symbol,
                        'type': 'price_level',
                        'title': 'Key Support Approached',
                        'message': f'{symbol} approaching support level at ${support:,.2f}',
                        'severity': 'low',
                        'timestamp': int(datetime.now().timestamp()),
                        'is_read': False,
                        'data': {
                            'level_type': 'support',
                            'level_price': support,
                            'current_price': current_price,
                            'distance_percent': distance_to_support * 100
                        }
                    })
            
            # Check resistance level
            resistance = key_levels.get('resistance')
            if resistance:
                distance_to_resistance = abs(current_price - resistance) / resistance
                if distance_to_resistance < 0.02:  # Within 2%
                    alerts.append({
                        'id': str(uuid.uuid4()),
                        'symbol': symbol,
                        'type': 'price_level',
                        'title': 'Key Resistance Approached',
                        'message': f'{symbol} approaching resistance level at ${resistance:,.2f}',
                        'severity': 'low',
                        'timestamp': int(datetime.now().timestamp()),
                        'is_read': False,
                        'data': {
                            'level_type': 'resistance',
                            'level_price': resistance,
                            'current_price': current_price,
                            'distance_percent': distance_to_resistance * 100
                        }
                    })
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error checking key level approaches for {symbol}: {e}")
            return []
    
    async def _save_alert(self, alert_data: Dict):
        """Save alert to database"""
        try:
            alert = Alert(
                id=alert_data['id'],
                symbol=alert_data['symbol'],
                type=alert_data['type'],
                title=alert_data['title'],
                message=alert_data['message'],
                severity=alert_data['severity'],
                timestamp=alert_data['timestamp'],
                is_read=alert_data['is_read'],
                data=alert_data.get('data', {})
            )
            
            self.db.add(alert)
            self.db.commit()
            
            logger.info(f"Saved alert: {alert_data['title']} for {alert_data['symbol']}")
            
        except Exception as e:
            logger.error(f"Error saving alert: {e}")
            self.db.rollback()
    
    async def get_alerts(self, limit: int = 10, offset: int = 0, 
                        symbol: Optional[str] = None, 
                        unread_only: bool = False) -> List[Dict]:
        """Get alerts from database"""
        try:
            query = self.db.query(Alert)
            
            if symbol:
                query = query.filter(Alert.symbol == symbol.upper())
            
            if unread_only:
                query = query.filter(Alert.is_read == False)
            
            alerts = query.order_by(Alert.timestamp.desc()).offset(offset).limit(limit).all()
            
            return [
                {
                    'id': alert.id,
                    'symbol': alert.symbol,
                    'type': alert.type,
                    'title': alert.title,
                    'message': alert.message,
                    'severity': alert.severity,
                    'timestamp': alert.timestamp,
                    'is_read': alert.is_read,
                    'data': alert.data
                }
                for alert in alerts
            ]
            
        except Exception as e:
            logger.error(f"Error getting alerts: {e}")
            return []
    
    async def mark_alert_as_read(self, alert_id: str):
        """Mark an alert as read"""
        try:
            alert = self.db.query(Alert).filter(Alert.id == alert_id).first()
            if alert:
                alert.is_read = True
                self.db.commit()
                logger.info(f"Marked alert {alert_id} as read")
            
        except Exception as e:
            logger.error(f"Error marking alert as read: {e}")
            self.db.rollback()
    
    async def delete_alert(self, alert_id: str):
        """Delete an alert"""
        try:
            alert = self.db.query(Alert).filter(Alert.id == alert_id).first()
            if alert:
                self.db.delete(alert)
                self.db.commit()
                logger.info(f"Deleted alert {alert_id}")
            
        except Exception as e:
            logger.error(f"Error deleting alert: {e}")
            self.db.rollback()
    
    async def generate_alerts_for_watchlist(self, symbols: List[str]) -> List[Dict]:
        """Generate alerts for a list of symbols"""
        all_alerts = []
        
        for symbol in symbols:
            try:
                alerts = await self.generate_alerts_for_symbol(symbol)
                all_alerts.extend(alerts)
            except Exception as e:
                logger.error(f"Error generating alerts for {symbol}: {e}")
                continue
        
        return all_alerts


def get_alert_service(db: Session) -> AlertService:
    """Get alert service instance"""
    return AlertService(db)
