"""
WebSocket API endpoints
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.websocket_service import get_websocket_service
import uuid
from loguru import logger

router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    client_id: str = Query(None),
    db: Session = Depends(get_db)
):
    """Main WebSocket endpoint for real-time data"""
    
    # Generate client ID if not provided
    if not client_id:
        client_id = str(uuid.uuid4())
    
    websocket_service = get_websocket_service(db)
    
    try:
        await websocket_service.handle_websocket(websocket, client_id)
    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")


@router.websocket("/ws/market")
async def market_websocket_endpoint(
    websocket: WebSocket,
    db: Session = Depends(get_db)
):
    """WebSocket endpoint specifically for market data updates"""
    
    client_id = f"market_{uuid.uuid4()}"
    websocket_service = get_websocket_service(db)
    
    await websocket.accept()
    
    try:
        # Auto-subscribe to major cryptocurrencies
        major_cryptos = ["BTC", "ETH", "BNB", "ADA", "SOL"]
        
        for symbol in major_cryptos:
            websocket_service.manager.subscribe(client_id, symbol)
        
        # Send initial data
        await websocket.send_text('{"type": "connected", "subscriptions": ["BTC", "ETH", "BNB", "ADA", "SOL"]}')
        
        # Keep connection alive and handle messages
        while True:
            try:
                data = await websocket.receive_text()
                # Handle any client messages if needed
                logger.info(f"Received market websocket message: {data}")
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"Market WebSocket error: {e}")
    finally:
        websocket_service.manager.disconnect(client_id)


@router.websocket("/ws/alerts")
async def alerts_websocket_endpoint(
    websocket: WebSocket,
    db: Session = Depends(get_db)
):
    """WebSocket endpoint specifically for alerts"""
    
    client_id = f"alerts_{uuid.uuid4()}"
    websocket_service = get_websocket_service(db)
    
    await websocket.accept()
    
    try:
        # Send initial connection message
        await websocket.send_text('{"type": "alerts_connected"}')
        
        # Keep connection alive
        while True:
            try:
                data = await websocket.receive_text()
                logger.info(f"Received alerts websocket message: {data}")
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"Alerts WebSocket error: {e}")
    finally:
        websocket_service.manager.disconnect(client_id)
