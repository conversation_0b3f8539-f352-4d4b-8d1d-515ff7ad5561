'use client';

import { useEffect } from 'react';
import { TrendingUp, TrendingDown, Activity, DollarSign, RefreshCw } from 'lucide-react';
import { formatCurrency, formatPercentage, getPriceChangeColor, formatMarketCap } from '@/lib/utils';
import { useMarketStore } from '@/stores/marketStore';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export function MarketOverview() {
  const {
    marketOverview,
    currencies,
    loading,
    fetchMarketOverview,
    fetchCurrencies,
    refreshAllData
  } = useMarketStore();

  useEffect(() => {
    fetchMarketOverview();
    fetchCurrencies({ limit: 5 });
  }, [fetchMarketOverview, fetchCurrencies]);

  const handleRefresh = async () => {
    await refreshAllData();
  };

  if (loading.isLoading && !marketOverview) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <LoadingSpinner />
      </div>
    );
  }

  const topCryptos = currencies.slice(0, 3);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Market Overview
        </h2>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <Activity className="h-4 w-4" />
            <span>Live</span>
          </div>
          <button
            onClick={handleRefresh}
            disabled={loading.isLoading}
            className="p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
            title="Refresh market data"
          >
            <RefreshCw className={`h-4 w-4 ${loading.isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {/* Market Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center">
            <DollarSign className="h-5 w-5 text-primary-600 dark:text-primary-400" />
            <span className="ml-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              Market Cap
            </span>
          </div>
          <div className="mt-2">
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {marketOverview ? formatMarketCap(marketOverview.total_market_cap) : '--'}
            </div>
            <div className={`text-sm ${getPriceChangeColor(marketOverview?.market_cap_change_24h || 0)}`}>
              {marketOverview?.market_cap_change_24h > 0 ? '+' : ''}
              {marketOverview ? formatPercentage(marketOverview.market_cap_change_24h) : '--'}
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center">
            <Activity className="h-5 w-5 text-primary-600 dark:text-primary-400" />
            <span className="ml-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              24h Volume
            </span>
          </div>
          <div className="mt-2">
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {marketOverview ? formatMarketCap(marketOverview.total_volume) : '--'}
            </div>
            <div className={`text-sm ${getPriceChangeColor(marketOverview?.volume_change_24h || 0)}`}>
              {marketOverview?.volume_change_24h > 0 ? '+' : ''}
              {marketOverview ? formatPercentage(marketOverview.volume_change_24h) : '--'}
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center">
            <TrendingUp className="h-5 w-5 text-primary-600 dark:text-primary-400" />
            <span className="ml-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              BTC Dominance
            </span>
          </div>
          <div className="mt-2">
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {marketOverview ? formatPercentage(marketOverview.btc_dominance) : '--'}
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center">
            <Activity className="h-5 w-5 text-primary-600 dark:text-primary-400" />
            <span className="ml-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              Fear & Greed
            </span>
          </div>
          <div className="mt-2">
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {marketOverview?.fear_greed_index || '--'}
            </div>
            <div className="text-sm text-warning-600">
              {marketOverview?.fear_greed_index >= 60 ? 'Greed' :
               marketOverview?.fear_greed_index >= 40 ? 'Neutral' : 'Fear'}
            </div>
          </div>
        </div>
      </div>

      {/* Top Cryptocurrencies */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Top Cryptocurrencies
        </h3>
        <div className="space-y-3">
          {topCryptos.length > 0 ? topCryptos.map((crypto) => (
            <div
              key={crypto.symbol}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                  {crypto.image ? (
                    <img
                      src={crypto.image}
                      alt={crypto.name}
                      className="w-6 h-6 rounded-full"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling!.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <span
                    className="text-sm font-medium text-white"
                    style={{ display: crypto.image ? 'none' : 'flex' }}
                  >
                    {crypto.symbol?.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {crypto.symbol}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {crypto.name}
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="font-medium text-gray-900 dark:text-white">
                  {crypto.current_price ? formatCurrency(crypto.current_price) : '--'}
                </div>
                <div className={`text-sm flex items-center ${getPriceChangeColor(crypto.price_change_percentage_24h || 0)}`}>
                  {(crypto.price_change_percentage_24h || 0) > 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {(crypto.price_change_percentage_24h || 0) > 0 ? '+' : ''}
                  {crypto.price_change_percentage_24h ? formatPercentage(crypto.price_change_percentage_24h) : '--'}
                </div>
              </div>
            </div>
          )) : (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400">
              Loading cryptocurrencies...
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
