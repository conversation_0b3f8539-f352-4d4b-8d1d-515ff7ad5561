"""
Unified market data service that combines multiple data sources
"""

from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from app.core.config import settings
from app.services.market_service import MarketService
from app.services.coinmarketcap_service import CoinMarketCapService
from loguru import logger


class UnifiedMarketService:
    """Unified service that combines data from multiple sources"""
    
    def __init__(self, db: Session):
        self.db = db
        self.coingecko_service = MarketService(db)
        self.coinmarketcap_service = CoinMarketCapService(db)
        
        # Determine primary and fallback services
        self.primary_service = "coingecko"  # Default to CoinGecko
        self.fallback_service = "coinmarketcap"
        
        # If CoinMarketCap API key is available, prefer it for some operations
        if settings.COINMARKETCAP_API_KEY:
            logger.info("CoinMarketCap API key available, using as primary for some operations")
    
    async def get_cryptocurrencies(
        self, 
        limit: int = 100, 
        offset: int = 0, 
        search: Optional[str] = None
    ) -> List[Dict]:
        """Get cryptocurrencies with fallback support"""
        try:
            # Try primary service first
            if self.primary_service == "coingecko":
                return await self.coingecko_service.get_cryptocurrencies(limit, offset, search)
            else:
                # Convert offset to start for CoinMarketCap
                start = offset + 1
                data = await self.coinmarketcap_service.get_cryptocurrencies(limit, start)
                
                # Apply search filter if provided
                if search:
                    search_lower = search.lower()
                    data = [
                        crypto for crypto in data 
                        if search_lower in crypto["name"].lower() or search_lower in crypto["symbol"].lower()
                    ]
                
                return data
                
        except Exception as e:
            logger.warning(f"Primary service failed: {str(e)}, trying fallback")
            
            # Try fallback service
            try:
                if self.fallback_service == "coinmarketcap":
                    start = offset + 1
                    data = await self.coinmarketcap_service.get_cryptocurrencies(limit, start)
                    
                    if search:
                        search_lower = search.lower()
                        data = [
                            crypto for crypto in data 
                            if search_lower in crypto["name"].lower() or search_lower in crypto["symbol"].lower()
                        ]
                    
                    return data
                else:
                    return await self.coingecko_service.get_cryptocurrencies(limit, offset, search)
                    
            except Exception as fallback_error:
                logger.error(f"Both services failed. Primary: {str(e)}, Fallback: {str(fallback_error)}")
                raise Exception(f"All market data services failed: {str(e)}")
    
    async def get_cryptocurrency_by_symbol(self, symbol: str) -> Optional[Dict]:
        """Get specific cryptocurrency with fallback support"""
        try:
            # Try primary service first
            if self.primary_service == "coingecko":
                result = await self.coingecko_service.get_cryptocurrency_by_symbol(symbol)
            else:
                result = await self.coinmarketcap_service.get_cryptocurrency_by_symbol(symbol)
            
            if result:
                return result
                
        except Exception as e:
            logger.warning(f"Primary service failed for {symbol}: {str(e)}")
        
        # Try fallback service
        try:
            if self.fallback_service == "coinmarketcap":
                return await self.coinmarketcap_service.get_cryptocurrency_by_symbol(symbol)
            else:
                return await self.coingecko_service.get_cryptocurrency_by_symbol(symbol)
                
        except Exception as e:
            logger.error(f"Fallback service also failed for {symbol}: {str(e)}")
            return None
    
    async def get_market_overview(self) -> Dict:
        """Get market overview with enhanced data from multiple sources"""
        try:
            # Try to get data from both sources and combine
            coingecko_data = None
            coinmarketcap_data = None
            
            # Get CoinGecko data
            try:
                coingecko_data = await self.coingecko_service.get_market_overview()
            except Exception as e:
                logger.warning(f"CoinGecko market overview failed: {str(e)}")
            
            # Get CoinMarketCap data if API key is available
            if settings.COINMARKETCAP_API_KEY:
                try:
                    coinmarketcap_data = await self.coinmarketcap_service.get_global_metrics()
                except Exception as e:
                    logger.warning(f"CoinMarketCap global metrics failed: {str(e)}")
            
            # Combine data, preferring CoinMarketCap for accuracy if available
            if coinmarketcap_data and coingecko_data:
                return {
                    "total_market_cap": coinmarketcap_data.get("total_market_cap") or coingecko_data.get("total_market_cap"),
                    "total_volume": coinmarketcap_data.get("total_volume") or coingecko_data.get("total_volume"),
                    "btc_dominance": coinmarketcap_data.get("btc_dominance") or coingecko_data.get("btc_dominance"),
                    "eth_dominance": coinmarketcap_data.get("eth_dominance"),
                    "market_cap_change_24h": coinmarketcap_data.get("market_cap_change_24h") or coingecko_data.get("market_cap_change_24h"),
                    "volume_change_24h": coinmarketcap_data.get("volume_change_24h") or coingecko_data.get("volume_change_24h"),
                    "fear_greed_index": coingecko_data.get("fear_greed_index"),  # CoinGecko calculation
                    "active_cryptocurrencies": coinmarketcap_data.get("active_cryptocurrencies") or coingecko_data.get("active_cryptocurrencies"),
                    "active_exchanges": coinmarketcap_data.get("active_exchanges"),
                    "markets": coingecko_data.get("markets"),
                    "data_sources": ["coingecko", "coinmarketcap"]
                }
            elif coinmarketcap_data:
                # Add fear & greed calculation
                market_cap_change = coinmarketcap_data.get("market_cap_change_24h", 0)
                if market_cap_change > 5:
                    fear_greed_index = 75
                elif market_cap_change > 2:
                    fear_greed_index = 65
                elif market_cap_change > -2:
                    fear_greed_index = 50
                elif market_cap_change > -5:
                    fear_greed_index = 35
                else:
                    fear_greed_index = 25
                
                coinmarketcap_data["fear_greed_index"] = fear_greed_index
                coinmarketcap_data["data_sources"] = ["coinmarketcap"]
                return coinmarketcap_data
            elif coingecko_data:
                coingecko_data["data_sources"] = ["coingecko"]
                return coingecko_data
            else:
                raise Exception("No market data available from any source")
                
        except Exception as e:
            logger.error(f"Error getting market overview: {str(e)}")
            raise Exception(f"Error fetching market overview: {str(e)}")
    
    async def get_price_history(
        self, 
        symbol: str, 
        timeframe: str = "1d", 
        days: int = 30
    ) -> List[Dict]:
        """Get price history (primarily from CoinGecko as it has better historical data)"""
        try:
            return await self.coingecko_service.get_price_history(symbol, timeframe, days)
        except Exception as e:
            logger.error(f"Error getting price history for {symbol}: {str(e)}")
            raise Exception(f"Error fetching price history: {str(e)}")
    
    async def get_trending_cryptocurrencies(self, limit: int = 10) -> List[Dict]:
        """Get trending cryptocurrencies with fallback support"""
        try:
            # Try CoinGecko trending first (has dedicated trending endpoint)
            return await self.coingecko_service.get_trending_cryptocurrencies(limit)
        except Exception as e:
            logger.warning(f"CoinGecko trending failed: {str(e)}, trying CoinMarketCap")
            
            # Fallback to CoinMarketCap top gainers
            try:
                return await self.coinmarketcap_service.get_trending_cryptocurrencies(limit)
            except Exception as fallback_error:
                logger.error(f"Both trending services failed: {str(e)}, {str(fallback_error)}")
                # Final fallback to regular top cryptocurrencies
                return await self.get_cryptocurrencies(limit=limit)
    
    async def track_cryptocurrency(self, symbol: str) -> Dict:
        """Add cryptocurrency to tracking list"""
        # This would update the database
        return {"symbol": symbol, "tracked": True}
    
    async def untrack_cryptocurrency(self, symbol: str) -> Dict:
        """Remove cryptocurrency from tracking list"""
        # This would update the database
        return {"symbol": symbol, "tracked": False}
    
    async def refresh_market_data(self) -> Dict:
        """Manually refresh market data"""
        return {"status": "refresh_initiated", "message": "Market data refresh started"}
