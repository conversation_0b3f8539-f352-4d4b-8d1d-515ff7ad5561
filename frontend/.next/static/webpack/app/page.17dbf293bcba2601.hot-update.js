"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarketOverview.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/MarketOverview.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketOverview: function() { return /* binding */ MarketOverview; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_marketStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/marketStore */ \"(app-pages-browser)/./src/stores/marketStore.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(app-pages-browser)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ MarketOverview auto */ \n\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MarketOverview() {\n    var _this = this;\n    _s();\n    var _useMarketStore = (0,_stores_marketStore__WEBPACK_IMPORTED_MODULE_3__.useMarketStore)(), marketOverview = _useMarketStore.marketOverview, currencies = _useMarketStore.currencies, loading = _useMarketStore.loading, fetchMarketOverview = _useMarketStore.fetchMarketOverview, fetchCurrencies = _useMarketStore.fetchCurrencies, refreshAllData = _useMarketStore.refreshAllData;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        fetchMarketOverview();\n        fetchCurrencies({\n            limit: 5\n        });\n    }, [\n        fetchMarketOverview,\n        fetchCurrencies\n    ]);\n    var handleRefresh = function() {\n        var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_5__._)(function() {\n            return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_6__.__generator)(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            refreshAllData()\n                        ];\n                    case 1:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function handleRefresh() {\n            return _ref.apply(this, arguments);\n        };\n    }();\n    if (loading.isLoading && !marketOverview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    var topCryptos = currencies.slice(0, 3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                        children: \"Market Overview\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Live\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRefresh,\n                                disabled: loading.isLoading,\n                                className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50\",\n                                title: \"Refresh market data\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 \".concat(loading.isLoading ? \"animate-spin\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Market Cap\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(marketOverview.total_market_cap) : \"--\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)((marketOverview === null || marketOverview === void 0 ? void 0 : marketOverview.market_cap_change_24h) || 0)),\n                                        children: [\n                                            (marketOverview === null || marketOverview === void 0 ? void 0 : marketOverview.market_cap_change_24h) > 0 ? \"+\" : \"\",\n                                            marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(marketOverview.market_cap_change_24h) : \"--\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"24h Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(marketOverview.total_volume) : \"--\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)((marketOverview === null || marketOverview === void 0 ? void 0 : marketOverview.volume_change_24h) || 0)),\n                                        children: [\n                                            (marketOverview === null || marketOverview === void 0 ? void 0 : marketOverview.volume_change_24h) > 0 ? \"+\" : \"\",\n                                            marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(marketOverview.volume_change_24h) : \"--\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"BTC Dominance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                    children: marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(marketOverview.btc_dominance) : \"--\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Fear & Greed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: (marketOverview === null || marketOverview === void 0 ? void 0 : marketOverview.fear_greed_index) || \"--\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-warning-600\",\n                                        children: (marketOverview === null || marketOverview === void 0 ? void 0 : marketOverview.fear_greed_index) >= 60 ? \"Greed\" : (marketOverview === null || marketOverview === void 0 ? void 0 : marketOverview.fear_greed_index) >= 40 ? \"Neutral\" : \"Fear\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"Top Cryptocurrencies\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: topCryptos.length > 0 ? topCryptos.map(function(crypto) {\n                            var _crypto_symbol;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                children: [\n                                                    crypto.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: crypto.image,\n                                                        alt: crypto.name,\n                                                        className: \"w-6 h-6 rounded-full\",\n                                                        onError: function(e) {\n                                                            e.currentTarget.style.display = \"none\";\n                                                            e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, _this) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        style: {\n                                                            display: crypto.image ? \"none\" : \"flex\"\n                                                        },\n                                                        children: (_crypto_symbol = crypto.symbol) === null || _crypto_symbol === void 0 ? void 0 : _crypto_symbol.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: crypto.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: crypto.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: crypto.current_price ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(crypto.current_price) : \"--\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm flex items-center \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)(crypto.price_change_percentage_24h || 0)),\n                                                children: [\n                                                    (crypto.price_change_percentage_24h || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, _this),\n                                                    (crypto.price_change_percentage_24h || 0) > 0 ? \"+\" : \"\",\n                                                    crypto.price_change_percentage_24h ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(crypto.price_change_percentage_24h) : \"--\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, _this)\n                                ]\n                            }, crypto.symbol, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, _this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-4 text-gray-500 dark:text-gray-400\",\n                            children: \"Loading cryptocurrencies...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketOverview, \"laftJeRTZmsbci9YOejB2aT2y3U=\", false, function() {\n    return [\n        _stores_marketStore__WEBPACK_IMPORTED_MODULE_3__.useMarketStore\n    ];\n});\n_c = MarketOverview;\nvar _c;\n$RefreshReg$(_c, \"MarketOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarketOverview.tsx\n"));

/***/ })

});