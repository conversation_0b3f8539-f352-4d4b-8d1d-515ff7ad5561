/**
 * WebSocket client for real-time data
 */

import { useEffect, useRef, useCallback } from 'react';
import { useMarketStore } from '@/stores/marketStore';
import toast from 'react-hot-toast';

interface WebSocketMessage {
  type: string;
  symbol?: string;
  data?: any;
  timestamp?: number;
}

class WebSocketClient {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private subscriptions = new Set<string>();
  private messageHandlers = new Map<string, (data: any) => void>();

  constructor(private url: string) {}

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
        resolve();
        return;
      }

      this.isConnecting = true;

      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // Re-subscribe to previous subscriptions
          this.subscriptions.forEach(symbol => {
            this.subscribe(symbol);
          });
          
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('WebSocket disconnected');
          this.isConnecting = false;
          this.ws = null;
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  private handleMessage(message: WebSocketMessage) {
    const handler = this.messageHandlers.get(message.type);
    if (handler) {
      handler(message);
    }

    // Default message handling
    switch (message.type) {
      case 'connection':
        console.log('WebSocket connection confirmed:', message);
        break;
      case 'price_update':
        console.log('Price update received:', message);
        break;
      case 'market_update':
        console.log('Market update received:', message);
        break;
      case 'alert':
        console.log('Alert received:', message);
        if (message.data) {
          toast.success(`Alert: ${message.data.title}`);
        }
        break;
      case 'pong':
        console.log('Pong received');
        break;
      default:
        console.log('Unknown message type:', message);
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  subscribe(symbol: string) {
    this.subscriptions.add(symbol);
    
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.send({
        type: 'subscribe',
        symbol: symbol
      });
    }
  }

  unsubscribe(symbol: string) {
    this.subscriptions.delete(symbol);
    
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.send({
        type: 'unsubscribe',
        symbol: symbol
      });
    }
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  ping() {
    this.send({ type: 'ping' });
  }

  onMessage(type: string, handler: (data: any) => void) {
    this.messageHandlers.set(type, handler);
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.subscriptions.clear();
    this.messageHandlers.clear();
  }

  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

// Global WebSocket client instance
let wsClient: WebSocketClient | null = null;

export function useWebSocket() {
  const wsRef = useRef<WebSocketClient | null>(null);
  const { setSelectedCurrency } = useMarketStore();

  const connect = useCallback(async () => {
    if (!wsRef.current) {
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000/api/v1/ws/ws';
      wsRef.current = new WebSocketClient(wsUrl);
      wsClient = wsRef.current;

      // Set up message handlers
      wsRef.current.onMessage('price_update', (message) => {
        console.log('Price update for', message.symbol, ':', message.data);
        // Update store with new price data
        // This would integrate with the market store
      });

      wsRef.current.onMessage('market_update', (message) => {
        console.log('Market update:', message.data);
        // Update market overview data
      });

      wsRef.current.onMessage('alert', (message) => {
        console.log('New alert:', message.data);
        // Handle new alerts
      });
    }

    try {
      await wsRef.current.connect();
      return true;
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      return false;
    }
  }, []);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.disconnect();
      wsRef.current = null;
      wsClient = null;
    }
  }, []);

  const subscribe = useCallback((symbol: string) => {
    if (wsRef.current) {
      wsRef.current.subscribe(symbol);
    }
  }, []);

  const unsubscribe = useCallback((symbol: string) => {
    if (wsRef.current) {
      wsRef.current.unsubscribe(symbol);
    }
  }, []);

  const isConnected = wsRef.current?.isConnected || false;

  useEffect(() => {
    // Auto-connect on mount
    connect();

    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    isConnected,
    client: wsRef.current
  };
}

// Export the global client for direct access
export { wsClient };
