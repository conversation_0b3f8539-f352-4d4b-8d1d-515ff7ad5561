"""
WebSocket service for real-time data streaming
"""

import asyncio
import json
import websockets
from typing import Dict, Set, Optional
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from app.core.config import settings
from app.services.unified_market_service import UnifiedMarketService
from loguru import logger
import time


class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.subscriptions: Dict[str, Set[str]] = {}  # client_id -> set of symbols
        
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.subscriptions[client_id] = set()
        logger.info(f"Client {client_id} connected. Total connections: {len(self.active_connections)}")
        
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.subscriptions:
            del self.subscriptions[client_id]
        logger.info(f"Client {client_id} disconnected. Total connections: {len(self.active_connections)}")
        
    async def send_personal_message(self, message: dict, client_id: str):
        """Send a message to a specific client"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)
                
    async def broadcast(self, message: dict):
        """Broadcast a message to all connected clients"""
        if not self.active_connections:
            return
            
        disconnected_clients = []
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
                
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
            
    async def broadcast_to_subscribers(self, symbol: str, message: dict):
        """Broadcast a message to clients subscribed to a specific symbol"""
        if not self.active_connections:
            return
            
        disconnected_clients = []
        for client_id, connection in self.active_connections.items():
            if symbol in self.subscriptions.get(client_id, set()):
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending to subscriber {client_id}: {e}")
                    disconnected_clients.append(client_id)
                    
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
            
    def subscribe(self, client_id: str, symbol: str):
        """Subscribe a client to a symbol"""
        if client_id in self.subscriptions:
            self.subscriptions[client_id].add(symbol.upper())
            logger.info(f"Client {client_id} subscribed to {symbol}")
            
    def unsubscribe(self, client_id: str, symbol: str):
        """Unsubscribe a client from a symbol"""
        if client_id in self.subscriptions:
            self.subscriptions[client_id].discard(symbol.upper())
            logger.info(f"Client {client_id} unsubscribed from {symbol}")


class WebSocketService:
    """Service for handling WebSocket connections and real-time data"""
    
    def __init__(self, db: Session):
        self.db = db
        self.market_service = UnifiedMarketService(db)
        self.manager = ConnectionManager()
        self.is_streaming = False
        self.stream_task: Optional[asyncio.Task] = None
        
    async def handle_websocket(self, websocket: WebSocket, client_id: str):
        """Handle WebSocket connection lifecycle"""
        await self.manager.connect(websocket, client_id)
        
        try:
            # Send initial connection message
            await self.manager.send_personal_message({
                "type": "connection",
                "status": "connected",
                "client_id": client_id,
                "timestamp": int(time.time())
            }, client_id)
            
            # Start streaming if not already started
            if not self.is_streaming:
                await self.start_price_streaming()
            
            # Listen for client messages
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                await self.handle_client_message(client_id, message)
                
        except WebSocketDisconnect:
            self.manager.disconnect(client_id)
        except Exception as e:
            logger.error(f"WebSocket error for client {client_id}: {e}")
            self.manager.disconnect(client_id)
            
    async def handle_client_message(self, client_id: str, message: dict):
        """Handle messages from clients"""
        message_type = message.get("type")
        
        if message_type == "subscribe":
            symbol = message.get("symbol", "").upper()
            if symbol:
                self.manager.subscribe(client_id, symbol)
                
                # Send current price data for the symbol
                try:
                    crypto_data = await self.market_service.get_cryptocurrency_by_symbol(symbol)
                    if crypto_data:
                        await self.manager.send_personal_message({
                            "type": "price_update",
                            "symbol": symbol,
                            "data": crypto_data,
                            "timestamp": int(time.time())
                        }, client_id)
                except Exception as e:
                    logger.error(f"Error fetching initial data for {symbol}: {e}")
                    
        elif message_type == "unsubscribe":
            symbol = message.get("symbol", "").upper()
            if symbol:
                self.manager.unsubscribe(client_id, symbol)
                
        elif message_type == "ping":
            await self.manager.send_personal_message({
                "type": "pong",
                "timestamp": int(time.time())
            }, client_id)
            
    async def start_price_streaming(self):
        """Start the price streaming background task"""
        if self.is_streaming:
            return
            
        self.is_streaming = True
        self.stream_task = asyncio.create_task(self.price_streaming_loop())
        logger.info("Started price streaming")
        
    async def stop_price_streaming(self):
        """Stop the price streaming background task"""
        self.is_streaming = False
        if self.stream_task:
            self.stream_task.cancel()
            try:
                await self.stream_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped price streaming")
        
    async def price_streaming_loop(self):
        """Main loop for streaming price updates"""
        while self.is_streaming:
            try:
                # Get all unique symbols that clients are subscribed to
                all_symbols = set()
                for symbols in self.manager.subscriptions.values():
                    all_symbols.update(symbols)
                
                if not all_symbols:
                    await asyncio.sleep(5)  # Wait if no subscriptions
                    continue
                
                # Fetch updated data for subscribed symbols
                for symbol in all_symbols:
                    try:
                        crypto_data = await self.market_service.get_cryptocurrency_by_symbol(symbol)
                        if crypto_data:
                            # Broadcast to subscribers
                            await self.manager.broadcast_to_subscribers(symbol, {
                                "type": "price_update",
                                "symbol": symbol,
                                "data": crypto_data,
                                "timestamp": int(time.time())
                            })
                            
                        # Small delay between symbols to avoid rate limiting
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        logger.error(f"Error fetching data for {symbol}: {e}")
                        continue
                
                # Wait before next update cycle (30 seconds)
                await asyncio.sleep(30)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in price streaming loop: {e}")
                await asyncio.sleep(5)
                
    async def broadcast_market_update(self, data: dict):
        """Broadcast general market updates to all clients"""
        await self.manager.broadcast({
            "type": "market_update",
            "data": data,
            "timestamp": int(time.time())
        })
        
    async def broadcast_alert(self, alert_data: dict):
        """Broadcast alerts to all clients"""
        await self.manager.broadcast({
            "type": "alert",
            "data": alert_data,
            "timestamp": int(time.time())
        })


# Global instance
websocket_service: Optional[WebSocketService] = None

def get_websocket_service(db: Session) -> WebSocketService:
    """Get or create WebSocket service instance"""
    global websocket_service
    if websocket_service is None:
        websocket_service = WebSocketService(db)
    return websocket_service
